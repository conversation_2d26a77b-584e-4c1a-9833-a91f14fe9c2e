import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/enums/cost_status_enum.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum CostFilter {
  all,
  pending,
  approved,
  rejected,
}

extension CostFilterX on CostFilter {
  String label(BuildContext context) {
    switch (this) {
      case CostFilter.all:
        return context.tr.common.all;
      case CostFilter.pending:
        return CostStatus.pending.label(context);
      case CostFilter.approved:
        return CostStatus.approved.label(context);
      case CostFilter.rejected:
        return CostStatus.rejected.label(context);
    }
  }

  CostStatus? toStatus() {
    switch (this) {
      case CostFilter.all:
        return null;
      case CostFilter.pending:
        return CostStatus.pending;
      case CostFilter.approved:
        return CostStatus.approved;
      case CostFilter.rejected:
        return CostStatus.rejected;
    }
  }
}
