part of '../costs_view.dart';

class _CostsTable extends HookConsumerWidget {
  const _CostsTable({this.status});

  final ExpenseStatusEnum? status;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(costsNotifierProvider(status));
    final notifier = ref.read(costsNotifierProvider(status).notifier);
    final style = ref.watch(appStyleProvider);
    final columns = <TableColumnModel>[
      TableColumnModel(columnName: context.tr.worker),
      TableColumnModel(columnName: context.tr.expenseType),
      TableColumnModel(columnName: context.tr.status),
      TableColumnModel(columnName: context.tr.amount, width: 150),
      TableColumnModel(columnName: context.tr.expenseDate, width: 170),
      TableColumnModel(columnName: context.tr.creationDate, width: 170),
      TableColumnModel(
        columnName: context.tr.documentLabel,
        width: 100,
        ignoreWhenExporting: true,
        filterable: false,
        sortable: false,
      ),
      TableColumnModel(
        columnName: context.tr.action,
        width: 300,
        ignoreWhenExporting: true,
        filterable: false,
        sortable: false,
      ),
    ];

    Widget buildDate(DateTime? date) {
      if (date == null) {
        return const SizedBox.shrink();
      }
      return Text(
        DTUtil.dtToString(
          date,
          format: DTFormat.dayMonthYear,
        ),
        style: style.text.bodyXSmall,
      );
    }

    List<List<RowDataModel<dynamic>>> getRowData(List<ExpenseClaim> expenses) {
      return expenses.map((expenseInfo) {
        return [
          RowDataModel<String>(
            columnName: columns[0].columnName,
            value: expenseInfo.mobileUser.name,
          ),
          RowDataModel<String>(
            columnName: columns[1].columnName,
            value: expenseInfo.expenseType.toString(),
          ),
          RowDataModel<String?>(
            columnName: columns[2].columnName,
            value: expenseInfo.status?.toString(),
          ),
          RowDataModel<double>(
            columnName: columns[3].columnName,
            value: expenseInfo.amount,
            cellBuilder: () => Text(
              '${expenseInfo.amount} ${expenseInfo.currency.symbol}',
              style: style.text.bodyXSmall,
            ),
          ),
          RowDataModel<DateTime?>(
            columnName: columns[4].columnName,
            value: expenseInfo.createdAt,
            cellBuilder: () {
              return buildDate(expenseInfo.createdAt);
            },
          ),
          RowDataModel<DateTime?>(
            columnName: columns[5].columnName,
            value: expenseInfo.dateSubmitted,
            cellBuilder: () {
              return buildDate(expenseInfo.dateSubmitted);
            },
          ),
          RowDataModel<void>(
            columnName: columns[6].columnName,
            cellBuilder: () => expenseInfo.receiptUrl != null
                ? IconButton(
                    icon: const Icon(Icons.search,
                        size: 24, color: AColor.textColor),
                    onPressed: () {},
                  )
                : const SizedBox.shrink(),
          ),
          RowDataModel<void>(
            columnName: columns[7].columnName,
            cellBuilder: () =>
                _ExpenseActions(expense: expenseInfo, status: status),
          ),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<PaginatedDataModel<ExpenseClaim>>(
      value: state,
      loadingWidget: const StDataTableLoading(showExportButtons: true),
      builder: (paginatedData) {
        return SmartTeamDataTable(
          columns: columns,
          rowData: getRowData(paginatedData.items),
          totalItemCount: paginatedData.total,
          onPageChanged: notifier.fetchPage,
        );
      },
    );
  }
}
