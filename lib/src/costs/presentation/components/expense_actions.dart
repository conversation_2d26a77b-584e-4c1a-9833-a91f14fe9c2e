part of '../costs_view.dart';

class _ExpenseActions extends HookConsumerWidget {
  const _ExpenseActions({required this.expense, this.status});

  final ExpenseClaim expense;
  final ExpenseStatusEnum? status;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final styles = ref.watch(appStyleProvider);
    final notifier = ref.watch(costsNotifierProvider(status).notifier);

    Widget buildActionButton({
      required String title,
      required Color color,
      required VoidCallback onPressed,
      required bool isSelected,
    }) {
      return InkWell(
        onTap: isSelected ? null : onPressed,
        child: Container(
          height: 30,
          width: 120,
          decoration: BoxDecoration(
            color: isSelected ? color : Colors.transparent,
            border: Border.all(color: color),
            borderRadius: BorderRadius.circular(styles.corners.lg),
          ),
          child: Center(
            child: Text(
              title,
              style: styles.text.bodyXSmall
                  .copyWith(color: isSelected ? styles.colors.white : color),
            ),
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        buildActionButton(
          title: expense.status == ExpenseStatusEnum.approved
              ? context.tr.approved
              : context.tr.approve,
          color: styles.colors.green,
          isSelected: expense.status == ExpenseStatusEnum.approved,
          onPressed: () {
            notifier.approveExpense(expense);
          },
        ),
        buildActionButton(
          title: expense.status == ExpenseStatusEnum.rejected
              ? context.tr.rejected
              : context.tr.reject,
          color: styles.colors.red,
          isSelected: expense.status == ExpenseStatusEnum.rejected,
          onPressed: () => notifier.rejectExpense(expense),
        ),
      ],
    );
  }
}
