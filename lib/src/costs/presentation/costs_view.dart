import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/costs/application/costs_provider.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_segmented_switcher.dart';
import 'package:smart_team_web/src/widgets/app_shimmer.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/widgets/loading/st_data_table_loading.dart';

part 'components/costs_table.dart';
part 'components/expense_actions.dart';
part 'components/status_tabs.dart';

@RoutePage(name: 'CostsRoute')
class CostsView extends HookConsumerWidget {
  const CostsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final styles = ref.watch(appStyleProvider);
    final tabIndex = useState(0);

    return Scaffold(
      body: SizedBox.expand(
        child: Padding(
          padding: EdgeInsets.all(styles.insets.lg),
          child: Column(
            spacing: 8,
            children: [
              _CostsTabs(onTabChange: (index) => tabIndex.value = index),
              Expanded(
                child: switch (tabIndex.value) {
                  0 => const _CostsTable(),
                  1 => const _CostsTable(status: ExpenseStatusEnum.pending),
                  2 => const _CostsTable(status: ExpenseStatusEnum.rejected),
                  3 => const _CostsTable(status: ExpenseStatusEnum.approved),
                  _ => const SizedBox.shrink(),
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
