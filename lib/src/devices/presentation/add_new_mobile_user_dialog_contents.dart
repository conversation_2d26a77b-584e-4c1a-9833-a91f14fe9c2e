import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/devices/application/add_mobile_user_provider.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/shared/providers/team_summaries_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';
import 'package:smart_team_web/src/widgets/dropdown/color_picker_dropdown.dart';
import 'package:smart_team_web/src/widgets/form_fields/phone_picker_formfield.dart';
import 'package:smart_team_web/src/widgets/new_dropdown/new_dropdown.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'widgets/switches_section.dart';
part 'widgets/form_section.dart';
part 'widgets/working_period_selector_section.dart';

class AddNewMobileUserDialogContents extends HookConsumerWidget {
  const AddNewMobileUserDialogContents({super.key, this.companyMobileUser});
  final CompanyMobileUserModel? companyMobileUser;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.addMobileUser));
    final style = ref.watch(appStyleProvider);

    return Form(
      key: formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: style.insets.sm,
          children: [
            _SwitchesSection(companyMobileUser: companyMobileUser),
            _FormSection(companyMobileUser: companyMobileUser),
            _WorkingPeriodSelectorSection(companyMobileUser: companyMobileUser),
            Align(
              alignment: Alignment.centerRight,
              child: LoadingElevatedButton(
                onPressed: () async {
                  if (!formKey.currentState!.validate()) return;
                  final isProcessed = await ref.executeWithLoading<bool>(
                    ref
                        .read(addMobileUserProvider(companyMobileUser).notifier)
                        .createOrUpdateMobileUser,
                  );
                  if (!context.mounted) return;
                  if (isProcessed) {
                    ref.read(toastManagerProvider).showToast(
                          companyMobileUser == null
                              ? context.tr.mobileUserCreatedSuccessfully
                              : context.tr.mobileUserUpdatedSuccessfully,
                        );
                    await context.maybePop(true);
                  } else {
                    ref.read(toastManagerProvider).showToast(
                          companyMobileUser == null
                              ? context.tr.mobileUserCreationFailed
                              : context.tr.mobileUserUpdateFailed,
                        );
                  }
                },
                text: context.tr.create,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
