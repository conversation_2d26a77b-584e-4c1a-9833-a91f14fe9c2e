import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/devices/application/company_mobile_users_provider.dart';
import 'package:smart_team_web/src/devices/presentation/add_new_mobile_user_dialog_contents.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';

class CompanyMobileUsersList extends HookConsumerWidget {
  const CompanyMobileUsersList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companyMobileUsersProvider =
        ref.watch(companyMobileUsersAsyncNotifierProvider);
    final tableColumnModels = [
      const TableColumnModel(
          columnName: '', sortable: false, filterable: false, width: 50),
      TableColumnModel(columnName: context.tr.gsm),
      TableColumnModel(columnName: context.tr.teamGroupShort),
      TableColumnModel(columnName: context.tr.driver),
      TableColumnModel(columnName: context.tr.applicationInstalled),
      TableColumnModel(columnName: context.tr.brand),
      TableColumnModel(columnName: context.tr.model),
      TableColumnModel(columnName: context.tr.version),
      const TableColumnModel(
        columnName: '',
        width: 220,
        sortable: false,
        filterable: false,
      ),
    ];

    List<List<RowDataModel<dynamic>>> getRowData(
        {required List<CompanyMobileUserModel> companyMobileUsers}) {
      return companyMobileUsers.map((companyMobileUser) {
        return [
          RowDataModel<void>(
            columnName: tableColumnModels.first.columnName,
            cellBuilder: () {
              final calendarColor =
                  companyMobileUser.mobileUserConfig?.calendarColor;
              if (calendarColor == null) return const SizedBox.shrink();
              return Center(
                child: Container(
                  width: 20,
                  height: 10,
                  color: calendarColor,
                ),
              );
            },
          ),
          RowDataModel<String>(
            columnName: tableColumnModels[1].columnName,
            value: companyMobileUser.mobileUser.phone ?? '',
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[2].columnName,
            value: companyMobileUser.team?.name,
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[3].columnName,
            value: companyMobileUser.mobileUser.name,
          ),
          RowDataModel<bool>(
            columnName: tableColumnModels[4].columnName,
            value: companyMobileUser.mobileUserConfig?.appRegistered ?? false,
            cellBuilder: () {
              return Center(
                child: Icon(
                  (companyMobileUser.mobileUserConfig?.appRegistered ?? false)
                      ? Icons.check
                      : Icons.close,
                ),
              );
            },
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[5].columnName,
            value: companyMobileUser.mobileDevice?.osVersion,
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[6].columnName,
            value: companyMobileUser.mobileDevice?.deviceModel,
          ),
          RowDataModel<String?>(
            columnName: tableColumnModels[7].columnName,
            value: companyMobileUser.mobileDevice?.appVersion,
          ),
          RowDataModel<void>(
            columnName: tableColumnModels[8].columnName,
            cellBuilder: () {
              return Center(
                child: LoadingElevatedButton(
                  height: 30,
                  text: context.tr.edit,
                  backgroundColor: AColor.white,
                  borderColor: AColor.loginBorderColor,
                  textColor: AColor.mineShaft,
                  onPressed: () async {
                    final isUpdated = await AppDialog.show<bool?>(
                      context: context,
                      title: context.tr.newUser,
                      width: context.width * .95,
                      height: context.height * .95,
                      child: AddNewMobileUserDialogContents(
                          companyMobileUser: companyMobileUser),
                    );
                    if (isUpdated != null && isUpdated) {
                      ref.invalidate(companyMobileUsersAsyncNotifierProvider);
                    }
                  },
                ),
              );
            },
          ),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<PaginatedDataModel<CompanyMobileUserModel>>(
      value: companyMobileUsersProvider,
      loadingWidget: const SizedBox.shrink(),
      builder: (paginatedData) {
        return SmartTeamDataTable(
          showExportButtons: false,
          columns: tableColumnModels,
          rowData: getRowData(companyMobileUsers: paginatedData.items),
          totalItemCount: paginatedData.total,
          onPageChanged: ref
              .read(companyMobileUsersAsyncNotifierProvider.notifier)
              .fetchPage,
        );
      },
    );
  }
}
