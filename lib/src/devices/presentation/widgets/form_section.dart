part of '../add_new_mobile_user_dialog_contents.dart';

final class _FormSection extends HookConsumerWidget {
  const _FormSection({this.companyMobileUser});
  final CompanyMobileUserModel? companyMobileUser;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);
    final state = ref.watch(addMobileUserProvider(companyMobileUser));
    final notifier =
        ref.read(addMobileUserProvider(companyMobileUser).notifier);
    final teamSummariesProvider = ref.watch(teamSummariesFutureProvider);
    final nameController = useTextEditingController(text: state.name);
    final surnameController = useTextEditingController(text: state.surname);

    return Column(
      spacing: style.insets.sm,
      children: [
        Row(
          spacing: style.insets.sm,
          children: [
            Expanded(
              child: PhonePicker<PERSON><PERSON><PERSON><PERSON>(
                headerText: context.tr.gsm,
                onPhoneNumberChanged: (value) {
                  notifier
                    ..setCountryCode(value.countryCode)
                    ..setPhoneNumber(value.phoneNumber);
                },
                phoneNumberInfo: (
                  countryCode: state.countryCode,
                  phoneNumber: state.phoneNumber,
                ),
              ),
            ),
            Expanded(
              child: CustomTextFormField(
                controller: nameController,
                headerText: context.tr.name,
                onChanged: notifier.setName,
                validator: (value) => value?.isValidName(context),
              ),
            ),
            Expanded(
              child: CustomTextFormField(
                controller: surnameController,
                headerText: context.tr.surname,
                onChanged: notifier.setSurname,
                validator: (value) => value.isValidName(context),
              ),
            ),
          ],
        ),
        Row(
          spacing: style.insets.sm,
          children: [
            Expanded(
              child: BaseAsyncProviderWidget<List<TeamSummaryModel>>(
                value: teamSummariesProvider,
                builder: (teamSummaries) {
                  return NewDropDown<TeamSummaryModel>(
                    prefix: state.teamSummary != null
                        ? InkWell(
                            onTap: () {
                              notifier.setTeamSummary(null);
                            },
                            child: const Icon(
                              Icons.close,
                              size: 16,
                            ),
                          )
                        : null,
                    placeholder: context.tr.teamSelect,
                    header: context.tr.team,
                    itemList: teamSummaries,
                    selectedItems:
                        state.teamSummary != null ? [state.teamSummary!] : [],
                    onSelected: notifier.setTeamSummary,
                  );
                },
              ),
            ),
            Expanded(
              child: ColorPickerDropDown(
                selectedColor:
                    state.config.calendarColor ?? AColor.primaryColor,
                header: context.tr.calendarColor,
                onColorSelected: notifier.setCalendarColor,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
