part of '../new_device_widgets.dart';

class AddGroupWidgets extends HookConsumerWidget {
  const AddGroupWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final textController = useTextEditingController();

    void saveGroupRecord() {
      if (formKey.currentState?.validate() ?? false) {
        final newName = textController.text.trim();
        if (newName.isNotEmpty) {
          ref.read(groupsProvider.notifier).addGroup(newName);
        }
        Navigator.pop(context);
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              context.tr.groupRecordAdd,
              style: ATextStyle.text12Bold,
            ),
            Container(
              decoration: CommonDecorations.containerDecoration(),
              child: CustomTextFormField(
                headerText: context.tr.groupName,
                controller: textController,
                keyboardType: TextInputType.name,
                textInputAction: TextInputAction.next,
                validator: (value) => value.isValidName(context),
                regexType: RegexType.name,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.white,
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(color: AColor.primaryColor),
                      ),
                      child: Text(
                        context.tr.cancel,
                        style: ATextStyle.text12
                            .copyWith(color: AColor.primaryColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: saveGroupRecord,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        context.tr.add,
                        style: ATextStyle.text12.copyWith(color: AColor.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
