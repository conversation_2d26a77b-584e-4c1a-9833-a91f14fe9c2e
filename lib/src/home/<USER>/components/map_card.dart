import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/home/<USER>/models/all_team_list.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/image/asset_image.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'map/custom_info_window.dart';
part 'map/map_utils.dart';
part 'map/ping_image.dart';

class MapCard extends StatefulWidget {
  const MapCard({required this.locations, super.key});

  final List<AllTeamList> locations;

  @override
  MapCardState createState() => MapCardState();
}

class MapCardState extends State<MapCard> with SingleTickerProviderStateMixin {
  AllTeamList? _hoveredUser;
  AllTeamList? _selectedUser;
  AnimationController? _animationController;
  Animation<double>? widthAnimation;
  GoogleMapController? _mapController;
  bool _isMarkersLoaded = false;

  void _updateCameraBounds() async {
    if (_mapController == null || widget.locations.isEmpty) return;
    final lats = widget.locations.map((u) => u.lat);
    final lngs = widget.locations.map((u) => u.lng);
    final southwest = LatLng(lats.reduce(min), lngs.reduce(min));
    final northeast = LatLng(lats.reduce(max), lngs.reduce(max));
    final bounds = LatLngBounds(southwest: southwest, northeast: northeast);
    await _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 50),
    );
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    widthAnimation = Tween<double>(begin: 40, end: 220).animate(
      CurvedAnimation(parent: _animationController!, curve: Curves.easeOut),
    );
    _loadMarkers();
  }

  @override
  void didUpdateWidget(MapCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.locations != widget.locations && _mapController != null) {
      _loadMarkers();
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _loadMarkers() async {
    setState(() {
      _isMarkersLoaded = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final defaultLatLng = widget.locations.isNotEmpty
        ? LatLng(widget.locations.first.lat, widget.locations.first.lng)
        : const LatLng(41.015137, 28.979530);

    if (!_isMarkersLoaded) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      decoration: CommonDecorations.containerDecoration(),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(
                  widget.locations.first.lat,
                  widget.locations.first.lng,
                ),
                zoom: 6,
              ),
              onMapCreated: (controller) {
                _mapController = controller;
                WidgetsBinding.instance
                    .addPostFrameCallback((_) => _updateCameraBounds());
                setState(() {});
              },
              onTap: (_) {
                setState(() {
                  _selectedUser = null;
                  _hoveredUser = null;
                });
                _animationController?.reverse();
              },
              onCameraMove: (position) {
                setState(() {});
              },
            ),
            ...widget.locations.map(_buildPingOverlay),
            ...widget.locations
                .map((user) => _buildProfileHoverArea(context, user)),
            Positioned(
              top: 8,
              right: 8,
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
                child: IconButton(
                  icon: const Icon(Icons.fullscreen),
                  tooltip: context.tr.common.mapScreen,
                  color: AColor.textColor,
                  onPressed: () {
                    context.router.push(
                      DynamicMapRoute(allTeamLocations: widget.locations),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPingOverlay(AllTeamList user) {
    return FutureBuilder<Offset>(
      future: _getPixelFromLatLng(LatLng(user.lat, user.lng)),
      builder: (context, snap) {
        if (!snap.hasData) return const SizedBox.shrink();
        final pos = snap.data!;
        const size = 24.0;
        return Positioned(
          left: pos.dx - size / 2,
          top: pos.dy - size / 2,
          child: PingImage(
            imgPath: user.status
                ? SmartTeamAssets.images.walking.path
                : SmartTeamAssets.images.stopping.path,
            size: size,
          ),
        );
      },
    );
  }

  Widget _buildProfileHoverArea(BuildContext context, AllTeamList user) {
    return FutureBuilder<Offset>(
      future: _getPixelFromLatLng(LatLng(user.lat, user.lng)),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final position = snapshot.data!;
        final state = user.status ? 'sürüş' : 'durma';

        return Positioned(
          left: position.dx + 50,
          top: position.dy,
          child: MouseRegion(
            onEnter: (_) {
              setState(() {
                _hoveredUser = user;
              });
              _animationController?.forward();
            },
            onExit: (_) {
              Future.delayed(const Duration(milliseconds: 300), () {
                if (_hoveredUser == user && _selectedUser != user) {
                  setState(() {
                    _hoveredUser = null;
                  });
                  _animationController?.reverse();
                }
              });
            },
            child: _hoveredUser == user
                ? _buildExpandingProfilePopup(context, user, state)
                : Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AColor.white,
                      border: Border.all(color: AColor.white),
                    ),
                    child: Icon(
                      Icons.account_circle,
                      color: CustomInfoWindow.getStateColor(state),
                      size: 48,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildExpandingProfilePopup(
    BuildContext context,
    AllTeamList user,
    String state,
  ) {
    return AnimatedBuilder(
      animation: _animationController!,
      builder: (context, child) {
        final width = widthAnimation!.value;
        const padding = 8.0;
        const maxAvatar = 60.0;
        final available = width - padding * 2;
        final avatarSize =
            available < maxAvatar ? available.clamp(0.0, maxAvatar) : maxAvatar;

        return ClipRect(
          child: Container(
            width: width,
            decoration: BoxDecoration(
              color: AColor.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Padding(
              padding: const EdgeInsets.all(padding),
              child: Row(
                children: [
                  Container(
                    width: avatarSize,
                    height: avatarSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: CustomInfoWindow.getStateColor(state),
                    ),
                    child: const Icon(
                      Icons.person,
                      color: AColor.white,
                      size: 24,
                    ),
                  ),
                  if (width > 70)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            TextWidget(
                              user.name,
                              textAlign: TextAlign.right,
                              style: ATextStyle.text12.copyWith(
                                color: AColor.black,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                            ),
                            TextWidget(
                              DTUtil.dtToString(
                                user.dataDateTime,
                                format: DTFormat.human1,
                              ),
                              textAlign: TextAlign.right,
                              style: ATextStyle.small,
                              maxLines: 1,
                            ),
                            TextWidget(
                              '${user.speed.toStringAsFixed(1)} ${context.tr.kmh}',
                              textAlign: TextAlign.right,
                              style: ATextStyle.small,
                              maxLines: 1,
                            ),
                            TextWidget(
                              user.stAddress,
                              textAlign: TextAlign.right,
                              style: ATextStyle.small,
                              maxLines: 1,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<Offset> _getPixelFromLatLng(LatLng latLng) async {
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;

    if (_mapController == null) return Offset.zero;
    try {
      final screenCoordinate =
          await _mapController!.getScreenCoordinate(latLng);
      return Offset(
        screenCoordinate.x.toDouble(),
        screenCoordinate.y.toDouble(),
      );
      /* return Offset(
        (screenCoordinate.x.toDouble() / pixelRatio) - 16,
        (screenCoordinate.y.toDouble() / pixelRatio) - 16,
      ); */
    } catch (e) {
      return Offset.zero;
    }
  }
}
