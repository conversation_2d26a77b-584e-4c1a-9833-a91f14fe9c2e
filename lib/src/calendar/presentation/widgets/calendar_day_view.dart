part of '../calendar_view.dart';

class CalendarDayView extends HookConsumerWidget {
  const CalendarDayView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDay = ref.watch(calendarSelectedDayProvider);
    final isFullDay = useState<bool>(false);

    const defaultStartHour = 8;
    const defaultEndHour = 16;

    final dayStart = DateTime(
      selectedDay.year,
      selectedDay.month,
      selectedDay.day,
      isFullDay.value ? 0 : defaultStartHour,
    );
    final dayEnd = DateTime(
      selectedDay.year,
      selectedDay.month,
      selectedDay.day,
      isFullDay.value ? 23 : defaultEndHour,
      30,
    );

    final hourSlots = _generateHourSlots(dayStart, dayEnd);
    const slotHeight = 50.0;
    final tasksForDay = ref.watch(tasksForCalendarViewProvider);

    return Column(
      children: [
        _buildDayHeader(context, ref, selectedDay, isFullDay),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final totalWidth = constraints.maxWidth;
            const totalColumns = 2;
            final columnWidth = totalWidth / totalColumns;
            final leftWidth = columnWidth / 8;
            final rightWidth = totalWidth - leftWidth;
            final totalHeight = slotHeight + hourSlots.length * 2 * slotHeight;

            final now = DateTime.now();
            final redLineOffset = calculateRedLineOffset(
              now,
              dayStart,
              slotHeight,
              hourSlots,
            );

            return SingleChildScrollView(
              child: SizedBox(
                width: totalWidth,
                height: totalHeight,
                child: Stack(
                  children: [
                    // 1) Saat ve boş hücre arka planı
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Sol saat kolonu
                        SizedBox(
                          width: leftWidth,
                          child: Column(
                            children: [
                              Container(
                                height: slotHeight,
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                ),
                              ),
                              for (final hour in hourSlots)
                                Container(
                                  height: 2 * slotHeight,
                                  width: leftWidth,
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      TextWidget(
                                        "${hour.hour.toString().padLeft(2, '0')}:00",
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      TextWidget(
                                        "${hour.hour.toString().padLeft(2, '0')}:30",
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // Sağ gün kolonu
                        SizedBox(
                          width: rightWidth,
                          child: Column(
                            children: [
                              // Gün başlığı
                              Container(
                                height: slotHeight,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                ),
                                child: TextWidget(
                                  _getDayHeaderLabel(context, selectedDay),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              // Boş hücreler
                              for (final _ in hourSlots) ...[
                                _emptySlot(slotHeight),
                                _emptySlot(slotHeight),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),

                    // 2) Görev kartları
                    ..._buildDayTasks(
                      context: context,
                      selectedDay: selectedDay,
                      dayStart: dayStart,
                      hourSlots: hourSlots,
                      slotHeight: slotHeight,
                      leftWidth: leftWidth,
                      rightWidth: rightWidth,
                      tasksForDay: tasksForDay,
                    ),

                    // 3) Kırmızı canlı çizgi
                    Positioned(
                      top: redLineOffset + slotHeight,
                      left: leftWidth,
                      right: 0,
                      child: Container(height: 2, color: Colors.red),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // ----------  PRIVATE  ----------

  Widget _emptySlot(double height) => GestureDetector(
        onDoubleTap: () {},
        child: Container(
          height: height,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
          ),
        ),
      );

  List<Widget> _buildDayTasks({
    required BuildContext context,
    required DateTime selectedDay,
    required DateTime dayStart,
    required List<DateTime> hourSlots,
    required double slotHeight,
    required double leftWidth,
    required double rightWidth,
    required List<Task> tasksForDay,
  }) {
    final widgets = <Widget>[];
    final dayTasks =
        tasksForDay.where((t) => _isSameDay(t.dueDate, selectedDay)).toList();
    final layouts = _computeTaskLayouts(dayTasks);

    for (final layout in layouts) {
      final task = layout.task;
      final top =
          _calculateTaskTopOffset(task, dayStart, slotHeight) + slotHeight;
      final height = _calculateTaskHeight(task, slotHeight);

      final totCols = layout.totalColumns;
      final singleWidth = rightWidth / totCols;
      final left = leftWidth + singleWidth * layout.column + 2;
      final width = singleWidth - 4;

      widgets.add(
        Positioned(
          top: top,
          left: left,
          width: width,
          height: height,
          child: CustomTooltip(
            tooltipOffset: 5,
            tooltipContent: buildCustomTooltipContent({
              context.tr.user: task.createdByUser.name,
              context.tr.title: task.title,
              if (task.description?.isNotEmpty ?? false)
                context.tr.description: task.description!,
              if (task.customer != null && task.customer?.name != null)
                context.tr.customers: task.customer!.name,
            }),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AColor.primaryColor.withAlpha(120),
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextWidget(
                task.title,
                style: const TextStyle(fontSize: 11, color: Colors.white),
              ),
            ),
          ),
        ),
      );
    }
    return widgets;
  }

  // Yardımcı fonksiyonlar
  List<DateTime> _generateHourSlots(DateTime start, DateTime end) {
    final slots = <DateTime>[];
    var current = DateTime(start.year, start.month, start.day, start.hour);
    while (current.isBefore(end)) {
      slots.add(current);
      current = current.add(const Duration(hours: 1));
    }
    return slots;
  }

  bool _isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) return false;
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  List<_TaskLayout> _computeTaskLayouts(List<Task> tasks) {
    tasks.sort((a, b) => a.createdAt!.compareTo(b.createdAt!));
    final layouts = <_TaskLayout>[];
    final active = <_TaskLayout>[];

    for (final task in tasks) {
      active.removeWhere((layout) =>
          layout.task.completedAt!.isBefore(task.createdAt!) ||
          layout.task.completedAt!.isAtSameMomentAs(task.createdAt!));
      final used = active.map((l) => l.column).toSet();
      var col = 0;
      while (used.contains(col)) col++;
      final layout = _TaskLayout(task: task, column: col);
      active.add(layout);
      layouts.add(layout);
      final count = active.length;
      for (final l in active) {
        l.totalColumns = count;
      }
    }
    return layouts;
  }

  double _calculateTaskTopOffset(Task task, DateTime dayStart, double slotH) {
    final diff = task.createdAt!.difference(dayStart).inMinutes;
    return (diff / 30) * slotH;
  }

  double _calculateTaskHeight(Task task, double slotH) {
    final duration = task.completedAt!.difference(task.createdAt!).inMinutes;
    return (duration / 30) * slotH;
  }

  Widget _buildDayHeader(
    BuildContext context,
    WidgetRef ref,
    DateTime selectedDay,
    ValueNotifier<bool> isFullDay,
  ) {
    final day = selectedDay.day;
    // final monthName = monthName(context, selectedDay.month);
    final monthLabel = monthName(context, selectedDay.month);
    final year = selectedDay.year;
    final title = '$day $monthLabel $year';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Sol: Takvim navigasyon ve dropdown
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Row(
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(2),
                        ),
                        backgroundColor: Colors.grey.shade50,
                      ),
                      onPressed: () {
                        ref.read(calendarSelectedDayProvider.notifier).state =
                            DateTime.now();
                      },
                      child: TextWidget(context.tr.today),
                    ),
                    SizedBox(
                      width: 25,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                          backgroundColor: Colors.grey.shade50,
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: () {
                          ref.read(calendarSelectedDayProvider.notifier).state =
                              selectedDay.subtract(const Duration(days: 1));
                        },
                        child: const Icon(
                          Icons.arrow_left,
                          size: 20,
                          color: AColor.doveGray,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 25,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                          backgroundColor: Colors.grey.shade50,
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: () {
                          ref.read(calendarSelectedDayProvider.notifier).state =
                              selectedDay.add(const Duration(days: 1));
                        },
                        child: const Icon(
                          Icons.arrow_right,
                          size: 20,
                          color: AColor.doveGray,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              ACalendarDropDown(
                initialDate: selectedDay,
                displayText: title,
                onDateSelected: (date) {
                  ref.read(calendarSelectedDayProvider.notifier).state = date;
                },
              ),
            ],
          ),

          // Orta: Tam gün / mesai saatleri butonu
          ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              backgroundColor: Colors.grey.shade50,
            ),
            onPressed: () {
              isFullDay.value = !isFullDay.value;
            },
            icon: const Icon(Icons.schedule_rounded),
            label: TextWidget(
              isFullDay.value
                  ? context.tr.showBusinessHours
                  : context.tr.showAllDay,
            ),
          ),

          // Sağ: Takvim tipi seçici
          buildCalendarTypeSelector(ref, context, selectedDate: selectedDay),
        ],
      ),
    );
  }

  String _getDayHeaderLabel(BuildContext context, DateTime date) {
    final shortWeek = _getShortWeekdayName(context, date);
    return '$shortWeek ${date.month}/${date.day}';
  }

  String _getShortWeekdayName(BuildContext context, DateTime date) {
    final weekdays = [
      context.tr.monday,
      context.tr.tuesday,
      context.tr.wednesday,
      context.tr.thursday,
      context.tr.friday,
      context.tr.saturday,
      context.tr.sunday,
    ];
    return weekdays[(date.weekday % 7)].substring(0, 3);
  }
}

class _TaskLayout {
  _TaskLayout({required this.task, this.column = 0, this.totalColumns = 1});
  final Task task;
  int column;
  int totalColumns;
}
