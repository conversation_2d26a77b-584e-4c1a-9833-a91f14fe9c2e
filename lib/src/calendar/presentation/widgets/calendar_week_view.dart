part of '../calendar_view.dart';

class CalendarWeekView extends HookConsumerWidget {
  const CalendarWeekView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // <PERSON><PERSON><PERSON> gün, "tam gün mü" durumu vs.
    final selectedDay = ref.watch(calendarSelectedDayProvider);
    final isFullDay = useState<bool>(false);

    // Haftanın günlerini hesapla (Pazartesi - Pazar)
    final monday = _getStartOfWeek(selectedDay);
    final weekDays = List.generate(7, (i) => monday.add(Duration(days: i)));

    // <PERSON>ün başlangıcı ve bitişi (iş saatleri veya tam gün)
    const defaultStartHour = 8;
    final dayStart = DateTime(
      selectedDay.year,
      selectedDay.month,
      selectedDay.day,
      isFullDay.value ? 0 : defaultStartHour,
    );
    final dayEnd = DateTime(
      selectedDay.year,
      selectedDay.month,
      selectedDay.day,
      isFullDay.value ? 23 : 16,
      30,
    );

    const slotHeight = 50.0;
    final hourSlots = _generateHourSlots(dayStart, dayEnd);
    // toplam slot sayısı = hourSlots.length * 2
    // Toplam yükseklik = (hourSlots.length * 2) * slotHeight
    final totalHeight = hourSlots.length * 2 * slotHeight;
    const totalColumns = 8;

    final tasksForWeek = ref.watch(tasksForCalendarViewProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildWeekHeader(context, ref, selectedDay, isFullDay),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final totalWidth = constraints.maxWidth;
            final columnWidth = totalWidth / totalColumns;

            final now = DateTime.now();
            final redLineOffset =
                calculateRedLineOffset(now, dayStart, slotHeight, hourSlots);

            return SingleChildScrollView(
              child: SizedBox(
                width: totalWidth,
                height: totalHeight + slotHeight,
                child: Stack(
                  children: [
                    // --- 1) Arkaplanda satır-sütun düzeni (Row/Column) ---
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 1A) SOLDA SAAT GÖSTEREN ALAN
                        SizedBox(
                          width: columnWidth / 2,
                          child: Column(
                            children: [
                              // En üst "gün başlığı" satırı: (boş)
                              Container(
                                height: slotHeight,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                ),
                              ),
                              // Saatlerin listesi
                              for (final hour in hourSlots)
                                Container(
                                  height: 2 * slotHeight, // 2 yarım saat
                                  width: columnWidth,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      TextWidget(
                                        "${hour.hour.toString().padLeft(2, '0')}:00",
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      TextWidget(
                                        "${hour.hour.toString().padLeft(2, '0')}:30",
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // 1B) SAĞDA 7 GÜN SÜTUNU
                        ...weekDays.asMap().entries.map((entry) {
                          final dayIndex = entry.key;
                          final day = entry.value;
                          return SizedBox(
                            width: columnWidth,
                            child: Column(
                              children: [
                                // Gün başlığı (örneğin "Pzt 4/10")
                                Container(
                                  height: slotHeight,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  child: TextWidget(
                                    '${_getShortWeekdayName(context, day)} '
                                    '${day.month}/${day.day}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                // Her "saat" için 2 yarım saatlik hücre
                                for (final hour in hourSlots)
                                  Column(
                                    children: [
                                      GestureDetector(
                                        onDoubleTap: () {
                                          final tappedDateTime = DateTime(
                                            day.year,
                                            day.month,
                                            day.day,
                                            hour.hour,
                                          );
                                          showAddNewJobDialog(
                                            context: context,
                                            initialDate: tappedDateTime,
                                          );
                                        },
                                        child: Container(
                                          height: slotHeight,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: Colors.grey.shade300,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        height: slotHeight,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),

                    // --- 2) Task’leri "Positioned" olarak çizen widget’lar ---
                    ..._buildWeekTasks(
                      context: context,
                      ref: ref,
                      weekDays: weekDays,
                      tasksForWeek: tasksForWeek,
                      columnWidth: columnWidth,
                      slotHeight: slotHeight,
                      dayStartHour: isFullDay.value ? 0 : defaultStartHour,
                    ),

                    Positioned(
                      top: redLineOffset + slotHeight,
                      left: columnWidth / 2,
                      right: columnWidth / 2,
                      child: Container(
                        height: 2,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // ------------------------
  //     TASK ÇİZİMİ
  // ------------------------
  List<Widget> _buildWeekTasks({
    required BuildContext context,
    required WidgetRef ref,
    required List<DateTime> weekDays,
    required List<Task> tasksForWeek,
    required double columnWidth,
    required double slotHeight,
    required int dayStartHour,
  }) {
    final widgets = <Widget>[];

    // Her güne ait Task’leri al
    for (int dayIndex = 0; dayIndex < weekDays.length; dayIndex++) {
      final day = weekDays[dayIndex];

      // Sadece bu güne ait taskler
      final tasksForDay =
          tasksForWeek.where((task) => _isSameDay(task.dueDate, day)).toList();

      // Görevleri sütun sayısını hesaplayacak şekilde yerleştirmek için
      // eski kodunuzdaki "layout" mantığı:
      final layouts = _computeTaskLayouts(tasksForDay);

      // Bu günün dayStart'ı (örn. 08:00)
      final dayStartDate = DateTime(day.year, day.month, day.day, dayStartHour);

      // Her bir Task için "Positioned" widget üret
      for (final layout in layouts) {
        final task = layout.task;

        // Task’in ekrandaki dikey başlangıç offset’i
        final topOffset = _calculateTaskTopOffset(
              task,
              dayStartDate,
              slotHeight,
            )
            // + en üstteki "gün başlığı" satırı yüksekliği
            +
            slotHeight;

        // Task in ekranda kaplayacağı dikey yükseklik
        final taskHeight = _calculateTaskHeight(task, slotHeight);

        // Soldan offset: "Saat" sütununu saymazsak ilk gün, ikinci gün vb.
        //  => solda "yarım" columnWidth/2 var + dayIndex * columnWidth
        final leftOffset = (columnWidth / 2) + (dayIndex * columnWidth);

        // Kolonları "üst üste binerse" daraltmak istiyorsak:
        final totalCols = layout.totalColumns;
        final singleTaskWidth = columnWidth / totalCols;

        // Bu Task kaçıncı kolonda?
        final left = leftOffset + singleTaskWidth * layout.column + 2;
        final width = singleTaskWidth - 4;

        widgets.add(
          Positioned(
            top: topOffset,
            left: left,
            width: width,
            height: taskHeight,
            child: CustomTooltip(
              tooltipContent: buildCustomTooltipContent({
                context.tr.user: task.createdByUser.name,
                context.tr.title: task.title,
                if (task.description?.isNotEmpty ?? false)
                  context.tr.description: task.description!,
                if (task.customer != null && task.customer?.name != null)
                  context.tr.customers: task.customer!.name,
              }),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AColor.primaryColor.withAlpha(120),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: TextWidget(
                  task.title,
                  style: const TextStyle(fontSize: 10, color: Colors.white),
                ),
              ),
            ),
          ),
        );
      }
    }

    return widgets;
  }

  // Task'ler üst üste bindiğinde "her biri kaçıncı sütunda" yer alacak?
  List<_TaskLayout> _computeTaskLayouts(List<Task> tasks) {
    // Başlangıç zamanına göre sırala
    tasks.sort((a, b) => a.createdAt!.compareTo(b.createdAt!));

    final layouts = <_TaskLayout>[];
    final active = <_TaskLayout>[];

    for (final task in tasks) {
      // Geçmişte bitmiş (çakışması sona ermiş) task’leri çıkar
      active.removeWhere(
        (layout) =>
            layout.task.completedAt!.isBefore(task.createdAt!) ||
            layout.task.completedAt!.isAtSameMomentAs(task.createdAt!),
      );

      // Var olan kolonları dolaş (0, 1, 2...) boş bulduğumuz yere koyacağız
      final used = active.map((l) => l.column).toSet();
      var col = 0;
      while (used.contains(col)) {
        col++;
      }

      final layout = _TaskLayout(task: task, column: col);
      active.add(layout);
      layouts.add(layout);

      // Kaç aktif task varsa, hepsi o kadar sütuna paylaştırılır
      final activeCount = active.length;
      for (final l in active) {
        l.totalColumns = activeCount;
      }
    }

    return layouts;
  }

  double _calculateTaskTopOffset(
    Task task,
    DateTime dayStart,
    double slotHeight,
  ) {
    // dayStart ile task.beginning arasındaki dakika farkını bul
    final minutesFromStart = task.createdAt!.difference(dayStart).inMinutes;
    // Bizim yapımızda 30 dakikalık her slot slotHeight kadar yüksek.
    // 1 dakika => slotHeight/30 piksel
    final offset = (minutesFromStart / 30) * slotHeight;
    return offset < 0 ? 0 : offset; // negatifse 0'a sabitleyebilirsiniz
  }

  double _calculateTaskHeight(Task task, double slotHeight) {
    // Task’in süresi kaç dakika
    final durationMinutes =
        task.completedAt!.difference(task.createdAt!).inMinutes;
    // 30 dk bir slot => slotHeight
    final height = (durationMinutes / 30) * slotHeight;
    // En azından görünecek bir yükseklik
    return height < 10 ? 10 : height;
  }

  // ------------------------
  //     HEADER BÖLÜMÜ
  // ------------------------
  Widget _buildWeekHeader(
    BuildContext context,
    WidgetRef ref,
    DateTime selectedDay,
    ValueNotifier<bool> isFullDay,
  ) {
    final monday = _getStartOfWeek(selectedDay);
    final sunday = monday.add(const Duration(days: 6));

    final rangeText =
        '${monday.day} ${monthName(context, monday.month)} ${monday.year} ${weekName(context, monday.weekday)}'
        ' - '
        '${sunday.day} ${monthName(context, sunday.month)} ${sunday.year} ${weekName(context, sunday.weekday)}';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              // 1) Bugün düğmesi, ileri-geri haftalar
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Row(
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(2),
                        ),
                        backgroundColor: Colors.grey.shade50,
                      ),
                      onPressed: () {
                        ref.read(calendarSelectedDayProvider.notifier).state =
                            DateTime.now();
                      },
                      child: TextWidget(context.tr.today),
                    ),
                    SizedBox(
                      width: 25,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                          backgroundColor: Colors.grey.shade50,
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: () {
                          ref.read(calendarSelectedDayProvider.notifier).state =
                              monday.subtract(const Duration(days: 7));
                        },
                        child: const Icon(
                          Icons.arrow_left,
                          size: 20,
                          color: AColor.doveGray,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 25,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                          backgroundColor: Colors.grey.shade50,
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: () {
                          ref.read(calendarSelectedDayProvider.notifier).state =
                              monday.add(const Duration(days: 7));
                        },
                        child: const Icon(
                          Icons.arrow_right,
                          size: 20,
                          color: AColor.doveGray,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // 2) Haftalık tarih seçicisi
              ACalendarDropDown(
                initialDate: monday,
                displayText: rangeText,
                onDateSelected: (newMonday) {
                  ref.read(calendarSelectedDayProvider.notifier).state =
                      newMonday;
                },
              ),
            ],
          ),

          // 3) "Tam gün" veya "Sadece mesai saatlerini göster" butonu
          ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              backgroundColor: Colors.grey.shade50,
            ),
            onPressed: () {
              isFullDay.value = !isFullDay.value;
            },
            icon: const Icon(Icons.schedule_rounded),
            label: TextWidget(
              isFullDay.value
                  ? context.tr.showBusinessHours
                  : context.tr.showAllDay,
            ),
          ),

          // 4) Ayın/Günün/Haftanın seçildiği tip seçici buton (muhtemelen var)
          buildCalendarTypeSelector(ref, context, selectedDate: selectedDay),
        ],
      ),
    );
  }

  // ------------------------
  //     SAAT SLOT’LARI
  // ------------------------
  // Yarım değil tam saatlik liste (08:00, 09:00...) üretmek için
  List<DateTime> _generateHourSlots(DateTime start, DateTime end) {
    final slots = <DateTime>[];
    var current = DateTime(start.year, start.month, start.day, start.hour);
    while (current.isBefore(end)) {
      slots.add(current);
      current = current.add(const Duration(hours: 1));
    }
    return slots;
  }

  // Haftanın ilk günü (pazartesi) bul
  DateTime _getStartOfWeek(DateTime date) {
    final dayOfWeek = date.weekday;
    return date.subtract(Duration(days: dayOfWeek - 1));
  }

  // Örneğin "Pzt" / "Sal" gibi kısa gün ismi
  String _getShortWeekdayName(BuildContext context, DateTime date) {
    final weekdays = [
      context.tr.monday,
      context.tr.tuesday,
      context.tr.wednesday,
      context.tr.thursday,
      context.tr.friday,
      context.tr.saturday,
      context.tr.sunday,
    ];
    return weekdays[(date.weekday % 7)].substring(0, 3);
  }

  bool _isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) return false;
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}
