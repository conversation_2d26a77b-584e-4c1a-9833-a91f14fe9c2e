import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/calendar/application/calendar_provider.dart';
import 'package:smart_team_web/src/job_list/enum/job_priority.dart';
import 'package:smart_team_web/src/job_list/presentation/dialogs/add_new_job_widgets.dart';
import 'package:smart_team_web/src/permits/application/permit_calendar_provider.dart';
import 'package:smart_team_web/src/reports/constants/report_contants.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/constants/team_list_tree_constants.dart';
import 'package:smart_team_web/src/shared/enums/calendar_type_enum.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/calendar_dropdown/a_calendar_dropdown.dart';
import 'package:smart_team_web/src/widgets/employee_tree_view/employee_tree_view.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/job_calendar.dart';
part 'components/left_container.dart';
part 'utils/calendar_utils.dart';
part 'utils/custom_tooltip.dart';
part 'widgets/calendar_day_view.dart';
part 'widgets/calendar_month_view.dart';
part 'widgets/calendar_week_view.dart';

@RoutePage(name: 'CalendarRoute')
class CalendarView extends StatelessWidget {
  const CalendarView({super.key});

  @override
  Widget build(BuildContext context) {
    final spaceWidth = context.responsive(
          desktop: 24 * 2,
          tablet: 16 * 2,
        ) +
        16;

    return Scaffold(
      backgroundColor: AColor.backgroundColor,
      body: Padding(
        padding: context.responsive(
          desktop: const EdgeInsets.all(24),
          tablet: const EdgeInsets.all(16),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const LeftContainer(),
              LayoutBuilder(
                builder: (context, constraints) {
                  final leftWidth = ReportConstants.leftContainerWidth(context);
                  return SizedBox(
                    width: max(
                      context.width - (leftWidth + spaceWidth),
                      ReportConstants.rightContainerMinWidth,
                    ),
                    child: const SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _JobCalendar(),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
