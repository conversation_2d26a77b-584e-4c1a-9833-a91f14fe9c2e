import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/calendar/application/calendar_provider.dart';
import 'package:smart_team_web/src/customers/application/customer_provider.dart';
import 'package:smart_team_web/src/job_list/application/add_job_provider.dart';
import 'package:smart_team_web/src/job_list/application/job_list_provider.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/riverpod_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/shared/providers/mobile_users_for_company_provider.dart';
import 'package:smart_team_web/src/shared/providers/users_for_company_provider.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';
import 'package:smart_team_web/src/widgets/form_fields/date_time_picker_formfield.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

class AddNewJobWidgets extends HookConsumerWidget {
  const AddNewJobWidgets({
    super.key,
    this.task,
  });

  final Task? task;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.addJob));
    final state = ref.watch(addJobProvider(task));
    final notifier = ref.read(addJobProvider(task).notifier);
    final usersAsync = ref.watch(mobileUsersForCompanyProvider);
    final customersAsync = ref.watch(customersForCompanyProvider);

    final titleCtl = useTextEditingController(text: state.title);
    final descCtl = useTextEditingController(text: state.description);

    final priorities = ref.watch(taskPrioritiesProvider);

    Future<void> saveTask() async {
      if (!formKey.currentState!.validate()) return;
      final tasksCreated = await ref.executeWithLoading<bool>(() async {
        return notifier.createTasks();
      });
      if (!context.mounted) return;
      if (tasksCreated) {
        ref
            .read(toastManagerProvider)
            .showToast('İşler başarıyla oluşturuldu.'.hardcoded);
        ref
          ..invalidate(jobCountFutureProvider)
          ..invalidate(jobListNotifier);
        await context.maybePop();
      } else {
        ref
            .read(toastManagerProvider)
            .showToast('İşler oluşturulurken bir hata oluştu.'.hardcoded);
      }
    }

    Future<void> updateTask() async {
      if (!formKey.currentState!.validate()) return;
      final updatedTask = await ref.executeWithLoading<Task?>(() async {
        return notifier.updateTask();
      });
      if (!context.mounted) return;
      if (updatedTask != null) {
        ref.read(toastManagerProvider).showToast('İş güncellendi.'.hardcoded);
        await context.maybePop(updatedTask);
      } else {
        ref
            .read(toastManagerProvider)
            .showToast('İş güncellenirken bir hata oluştu.'.hardcoded);
      }
    }

    return Portal(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 8,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16,
                children: [
                  Expanded(
                    child:
                        BaseAsyncProviderWidget<PaginatedDataModel<MobileUser>>(
                      value: usersAsync,
                      builder: (page) {
                        return ANewDropdownFormField<MobileUser>(
                          header: context.tr.selectDevice,
                          placeholder: context.tr.select,
                          itemList: page.items,
                          isDisabled: task != null,
                          allowMultipleSelection: true,
                          selectedItems: state.assignedToUsers,
                          onSelected: notifier.addOrRemoveAssignedToUsers,
                          onLoadMore: ref
                              .read(usersForCompanyProvider.notifier)
                              .fetchNextUsers,
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: BaseAsyncProviderWidget<
                        PaginatedDataModel<CustomerModel>>(
                      value: customersAsync,
                      builder: (paginatedModel) {
                        return ANewDropdownFormField<CustomerModel>(
                          header: context.tr.customers,
                          placeholder: context.tr.select,
                          itemList: paginatedModel.items,
                          onLoadMore: ref
                              .read(customersForCompanyProvider.notifier)
                              .fetchNextCustomers,
                          selectedItems:
                              state.customer == null ? [] : [state.customer!],
                          onSelected: notifier.setCustomer,
                          validator: (value) => null,
                        );
                      },
                    ),
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16,
                children: [
                  Expanded(
                    child: BaseAsyncProviderWidget<List<TaskPriority>>(
                      value: priorities,
                      builder: (priorities) {
                        return ANewDropdownFormField<TaskPriority>(
                          header: context.tr.priorityLabel,
                          placeholder: context.tr.select,
                          itemList: priorities,
                          selectedItems:
                              state.priority == null ? [] : [state.priority!],
                          onSelected: notifier.setPriority,
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: CustomTextFormField(
                      headerText: context.tr.title,
                      controller: titleCtl,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.name,
                      validator: (value) => value.isValidField(context),
                      onChanged: notifier.setTitle,
                    ),
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16,
                children: [
                  Expanded(
                    child: DateTimePickerFormField(
                      header: context.tr.startTime,
                      startDate: DateTime.now(),
                      selectedDate: state.startDate,
                      mode: DateTimePickerMode.dateAndTime,
                      onSelected: notifier.setStartDate,
                    ),
                  ),
                  Expanded(
                    child: DateTimePickerFormField(
                      header: context.tr.endTime,
                      startDate: DateTime.now(),
                      selectedDate: state.dueDate,
                      mode: DateTimePickerMode.dateAndTime,
                      onSelected: notifier.setDueDate,
                      validator: (value) {
                        if (value == null) {
                          return context.tr.fieldRequired;
                        }
                        if (value.isBefore(state.startDate ?? DateTime.now())) {
                          return 'Bitiş tarihi başlangıç tarihinden önce olamaz.'
                              .hardcoded;
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              CustomTextFormField(
                headerText: context.tr.description,
                controller: descCtl,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.name,
                validator: (value) => value.isValidField(context),
                maxLines: 4,
                onChanged: notifier.setDescription,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: LoadingElevatedButton(
                  height: 45,
                  text: context.tr.save,
                  onPressed: task == null ? saveTask : updateTask,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
