part of '../job_list_view.dart';

class _JobTable extends HookConsumerWidget {
  const _JobTable({
    this.statuses,
    this.priorities,
  });
  final List<TaskStatusEnum>? statuses;
  final List<int>? priorities;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final jobListProvider = ref
        .watch(jobListNotifier((statuses: statuses, priorities: priorities)));
    final style = ref.watch(appStyleProvider);

    late final List<TableColumnModel> columns;

    columns = [
      TableColumnModel(columnName: context.tr.gsm),
      TableColumnModel(columnName: context.tr.name),
      TableColumnModel(columnName: context.tr.priorityLabel),
      TableColumnModel(columnName: context.tr.status),
      TableColumnModel(columnName: context.tr.title),
      TableColumnModel(columnName: context.tr.description),
      const TableColumnModel(columnName: 'Müşteri'),
      TableColumnModel(columnName: context.tr.beginning),
      TableColumnModel(columnName: context.tr.finish),
      const TableColumnModel(
        columnName: '',
        width: 100,
        ignoreWhenExporting: true,
        filterable: false,
        sortable: false,
      ),
    ];

    List<List<RowDataModel<dynamic>>> getRowData(List<Task> tasks) {
      return tasks.indexed.map((record) {
        final (index, task) = record;
        return [
          RowDataModel<String>(
            columnName: context.tr.gsm,
            value: task.createdByUser.phone ?? '',
          ),
          RowDataModel<String>(
            columnName: context.tr.name,
            value: task.assignedToUser.name,
            cellBuilder: () => Text(task.assignedToUser.name),
          ),
          RowDataModel<String>(
            columnName: context.tr.priorityLabel,
            value: task.priority.name,
          ),
          RowDataModel<String>(
            columnName: context.tr.status,
            value: task.status.name,
          ),
          RowDataModel<String>(
            columnName: context.tr.title,
            value: task.title,
          ),
          RowDataModel<String>(
            columnName: context.tr.description,
            value: task.description,
          ),
          RowDataModel<String?>(
            columnName: 'Müşteri',
            value: task.customer?.name ?? '',
          ),
          RowDataModel<DateTime?>(
            columnName: context.tr.beginning,
            value: task.startDate,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  task.startDate,
                  format: DTFormat.simple,
                ),
                style: style.text.bodyXSmall,
              );
            },
          ),
          RowDataModel<DateTime?>(
            columnName: context.tr.finish,
            value: task.dueDate,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  task.dueDate,
                  format: DTFormat.simple,
                ),
                style: style.text.bodyXSmall,
              );
            },
          ),
          RowDataModel<Widget>(
            columnName: '',
            cellBuilder: () => Row(
              children: [
                IconButton(
                  icon: SmartTeamAssets.icons.edit.svg(),
                  onPressed: () async {
                    final updatedTask = await AppDialog.show<Task?>(
                      context: context,
                      title: 'İşi Düzenle'.hardcoded,
                      width: context.width * .95,
                      height: context.height * .95,
                      child: AddNewJobWidgets(task: task),
                    );
                    if (updatedTask != null) {
                      await ref
                          .read(jobListNotifier(
                                  (statuses: statuses, priorities: priorities))
                              .notifier)
                          .updateTask(updatedTask, index);
                    }
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () async {
                    final isDeleteConfirmed =
                        await DeleteEntryDialog.show(context);
                    if (isDeleteConfirmed != null && isDeleteConfirmed) {
                      await ref
                          .read(jobListNotifier(
                                  (statuses: statuses, priorities: priorities))
                              .notifier)
                          .deleteTask(index);
                    }
                  },
                ),
              ],
            ),
          ),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<PaginatedDataModel<Task>>(
      value: jobListProvider,
      builder: (taskListData) {
        final tasks = taskListData.items;
        return SmartTeamDataTable(
          columns: columns,
          rowData: getRowData(tasks),
          onLoadNext: taskListData.hasMoreData
              ? () async {
                  await ref
                      .read(jobListNotifier(
                              (statuses: statuses, priorities: priorities))
                          .notifier)
                      .fetchNextPage();
                }
              : null,
        );
      },
    );
  }
}
