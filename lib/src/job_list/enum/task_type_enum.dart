import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum TaskTypeEnum {
  visit, // ziyaret
  collection, // tahsilat
  service, // servis
  onlineMeeting, // online toplantı
  phoneCall, // telefon görüşmesi
}

extension TaskTypeEnumX on TaskTypeEnum {
  String label(BuildContext context) {
    switch (this) {
      case TaskTypeEnum.visit:
        return context.tr.taskType.visit;
      case TaskTypeEnum.collection:
        return context.tr.taskType.collection;
      case TaskTypeEnum.service:
        return context.tr.taskType.service;
      case TaskTypeEnum.onlineMeeting:
        return context.tr.taskType.onlineMeeting;
      case TaskTypeEnum.phoneCall:
        return context.tr.taskType.phoneCall;
    }
  }
}
