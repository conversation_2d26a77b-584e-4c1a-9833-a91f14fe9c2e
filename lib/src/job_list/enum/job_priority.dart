import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum JobPriority {
  low,
  mid,
  high,
  critical,
}

extension JobPriorityX on JobPriority {
  String label(BuildContext context) {
    switch (this) {
      case JobPriority.low:
        return context.tr.priority.low;
      case JobPriority.mid:
        return context.tr.priority.medium;
      case JobPriority.high:
        return context.tr.priority.high;
      case JobPriority.critical:
        return context.tr.critical;
    }
  }
}

class DisplayJobPriority {
  DisplayJobPriority({
    required this.priority,
    required BuildContext context,
  }) : label = priority.label(context);

  final JobPriority priority;
  final String label;

  @override
  String toString() => label;

  static List<DisplayJobPriority> getList(BuildContext context) {
    return JobPriority.values
        .map(
          (priority) => DisplayJobPriority(
            priority: priority,
            context: context,
          ),
        )
        .toList();
  }
}
