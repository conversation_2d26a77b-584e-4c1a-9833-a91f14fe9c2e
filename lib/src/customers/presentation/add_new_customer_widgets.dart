import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/application/search_provider.dart';
import 'package:smart_team_web/src/customers/presentation/widgets/left_form_components.dart';
import 'package:smart_team_web/src/customers/presentation/widgets/search_places.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/form_type.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';

import 'package:smart_team_web/src/shared/providers/form_key_provider.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/users/application/mobile_users_by_company_provider.dart';
import 'package:smart_team_web/src/widgets/app_map.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'components/map_container.dart';
part 'widgets/search_result_list.dart';

class AddNewCustomerWidgets extends HookConsumerWidget {
  const AddNewCustomerWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = ref.watch(formKeyProvider(FormType.addCustomer));
    final usersAsync = ref.watch(mobileUsersByCompanyProvider);

    final customerNameController = useTextEditingController();
    final authorizedNameController = useTextEditingController();

    return Portal(
      child: PointerInterceptor(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 16,
                  children: [
                    Row(
                      spacing: 8,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child:
                              BaseAsyncProviderWidget<PaginatedDataModel<User>>(
                            value: usersAsync,
                            builder: (page) {
                              return Consumer(
                                builder: (_, innerRef, __) {
                                  final assignedUsers = innerRef.watch(
                                      addCustomerNotifierProvider.select(
                                          (state) => state.assignedToUsers));
                                  return ANewDropdownFormField<User>(
                                    header: context.tr.selectDevice,
                                    placeholder: context.tr.select,
                                    itemList: page.items,
                                    allowMultipleSelection: true,
                                    selectedItems: assignedUsers,
                                    onSelected: innerRef
                                        .read(addCustomerNotifierProvider
                                            .notifier)
                                        .addOrRemoveAssignedToUsers,
                                    onLoadMore: innerRef
                                        .read(mobileUsersByCompanyProvider
                                            .notifier)
                                        .fetchNextMobileUsers,
                                  );
                                },
                              );
                            },
                          ),
                        ),
                        const Spacer(),
                      ],
                    ),
                    const MapContainer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 16,
                      children: [
                        const Expanded(
                          child: LeftFormComponents(),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: 8,
                            children: [
                              Consumer(
                                builder: (_, innerRef, __) {
                                  final status = innerRef.watch(
                                      addCustomerNotifierProvider
                                          .select((state) => state.status));
                                  return ANewDropdownFormField<
                                      CustomerStatusEnum>(
                                    placeholder: context.tr.select,
                                    header: context.tr.type,
                                    itemList: CustomerStatusEnum.values,
                                    onSelected: ref
                                        .read(addCustomerNotifierProvider
                                            .notifier)
                                        .setStatus,
                                    selectedItems:
                                        status == null ? [] : [status],
                                  );
                                },
                              ),
                              CustomTextFormField(
                                headerText: context.tr.customerName,
                                controller: customerNameController,
                                validator: (value) =>
                                    value.isValidField(context),
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setCustomerName,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.authorizedName,
                                controller: authorizedNameController,
                                validator: (value) =>
                                    value.isValidField(context),
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setAuthorizedName,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.authorizedGSM,
                                hintText:
                                    context.tr.validation.phoneNumberExample,
                                keyboardType: TextInputType.number,
                                textInputAction: TextInputAction.next,
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setAuthorizedGSM,
                                validator: (value) =>
                                    value.isValidField(context),
                                regexType: RegexType.phone,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.authorizedEmail,
                                validator: (value) =>
                                    value.isValidMail(context),
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setAuthorizedEmail,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.contactName,
                                validator: (value) =>
                                    value.isValidField(context),
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setContactName,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.contactGSM,
                                hintText:
                                    context.tr.validation.phoneNumberExample,
                                keyboardType: TextInputType.number,
                                textInputAction: TextInputAction.next,
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setContactGSM,
                                validator: (value) =>
                                    value.isValidField(context),
                                regexType: RegexType.phone,
                              ),
                              CustomTextFormField(
                                headerText: context.tr.contactEmail,
                                validator: (value) =>
                                    value.isValidMail(context),
                                onChanged: ref
                                    .read(addCustomerNotifierProvider.notifier)
                                    .setContactEmail,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: LoadingElevatedButton(
                        height: 45,
                        text: context.tr.save,
                        onPressed: () async {
                          if (!formKey.currentState!.validate()) {
                            ref.read(toastManagerProvider).showToast(
                                  context.tr.validation.fillAllFields,
                                );
                            return;
                          }
                          await ref
                              .read(addCustomerNotifierProvider.notifier)
                              .saveCustomer();
                        },
                      ),
                    ),
                  ],
                ),
                Consumer(
                  builder: (_, innerRef, __) {
                    final isLoading = innerRef.watch(
                      addCustomerNotifierProvider
                          .select((state) => state.isLoading),
                    );
                    if (!isLoading) {
                      return const SizedBox.shrink();
                    }
                    return Positioned.fill(
                      child: ColoredBox(
                        color: Colors.grey.withValues(
                          alpha: .4,
                        ),
                        child: Center(
                          child: Text(
                            'Lokasyon bilgisi alınıyor...',
                            style: ATextStyle.text32SemiBold.copyWith(
                              color: AColor.primaryColor,
                            ),
                          )
                              .animate(
                                onPlay: (controller) =>
                                    controller.loop(reverse: true),
                              )
                              .shimmer(
                                duration: 1200.ms,
                                color: AColor.dustyGray,
                              ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
