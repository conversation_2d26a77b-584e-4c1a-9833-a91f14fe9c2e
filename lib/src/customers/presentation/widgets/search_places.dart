import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/customers/application/add_customer_notifier.dart';
import 'package:smart_team_web/src/customers/application/search_provider.dart';
import 'package:smart_team_web/src/customers/domain/google_predictions_model.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget_data.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

class SearchPlaces extends HookConsumerWidget {
  const SearchPlaces({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allowResultsVisibility = useState(false);
    final googleAutocompleteResults =
        ref.watch(googleAutocompleteResultsProvider);
    final focusNode = useFocusNode();
    final searchController = useTextEditingController();

    void listenFocusNode() {
      if (focusNode.hasFocus) {
        allowResultsVisibility.value = true;
      }
    }

    useEffect(
      () {
        focusNode.addListener(listenFocusNode);
        return () {
          focusNode.removeListener(listenFocusNode);
        };
      },
      [],
    );

    Future<void> onPredictionSelected(Prediction prediction) async {
      searchController.text = prediction.description;
      allowResultsVisibility.value = false;
      focusNode.unfocus();
      await ref
          .read(addCustomerNotifierProvider.notifier)
          .setPlaceDetailsById(prediction.placeId);
    }

    Widget buildResults(GooglePredictionsModel? predictionModel) {
      if (predictionModel != null &&
          predictionModel.predictions.isNotEmpty &&
          allowResultsVisibility.value) {
        return Container(
          width: double.infinity,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: AColor.white,
            boxShadow: [
              BoxShadow(
                color: AColor.doveGray.withValues(alpha: .25),
                offset: const Offset(0, 1),
              ),
            ],
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
          ),
          constraints: const BoxConstraints(
            maxHeight: 200,
          ),
          child: ListView.builder(
            itemBuilder: (context, index) {
              final prediction = predictionModel.predictions[index];
              return HoverBuilder(
                onTap: () {
                  onPredictionSelected(prediction);
                },
                builder: (isHovered, _) => Container(
                  color: isHovered
                      ? AColor.primaryColor.withValues(alpha: .2)
                      : AColor.white,
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(prediction.description),
                      ),
                      if (isHovered)
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            color: AColor.primaryColor,
                            size: 16,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
            itemCount: predictionModel.predictions.length,
          ),
        );
      }
      return const SizedBox.shrink();
    }

    Widget buildContents(GooglePredictionsModel? predictionModel,
        {bool isLoading = false}) {
      return Column(
        children: [
          CustomTextFormField(
            focusNode: focusNode,
            controller: searchController,
            hintText: context.tr.search,
            debounceDuration: const Duration(milliseconds: 500),
            prefixIcon: const Icon(Icons.search),
            suffixIcon: InkWell(
              onTap: ref
                  .read(addCustomerNotifierProvider.notifier)
                  .showCurrentLocation,
              child: isLoading
                  ? Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : Container(
                      padding: const EdgeInsets.all(4),
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        color: AColor.primaryColor.withValues(alpha: .2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        spacing: 4,
                        children: [
                          const Icon(
                            Icons.my_location,
                            color: AColor.primaryColor,
                          ),
                          Text(
                            context.tr.common.findMyLocation,
                            style: ATextStyle.text12SemiBold
                                .copyWith(color: AColor.primaryColor),
                          ),
                        ],
                      ),
                    ),
            ),
            onChanged: (value) {
              ref
                  .read(addCustomerNotifierProvider.notifier)
                  .setSearchQuery(value);
            },
          ),
          buildResults(predictionModel),
        ],
      );
    }

    return TapRegion(
      onTapOutside: (_) {
        allowResultsVisibility.value = false;
      },
      child: SizedBox(
        width: 600,
        child: BaseAsyncProviderWidgetData<GooglePredictionsModel?>(
          value: googleAutocompleteResults,
          loadingBuilder: (predictionAsyncLoading) =>
              buildContents(predictionAsyncLoading.value, isLoading: true),
          builder: buildContents,
        ),
      ),
    );
  }
}
