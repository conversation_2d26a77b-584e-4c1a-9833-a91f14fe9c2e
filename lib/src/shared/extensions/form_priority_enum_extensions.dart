import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

extension FormPriorityEnumX on FormPriorityEnum {
  String getLocalizedName(BuildContext context) {
    switch (this) {
      case FormPriorityEnum.low:
        return context.tr.priority.low;
      case FormPriorityEnum.medium:
        return context.tr.priority.medium;
      case FormPriorityEnum.high:
        return context.tr.priority.high;
    }
  }
}
