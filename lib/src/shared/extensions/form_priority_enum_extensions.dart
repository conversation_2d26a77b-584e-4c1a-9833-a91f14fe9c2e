import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

extension FormPriorityEnumX on FormPriorityEnum {
  String getLocalizedName() {
    switch (this) {
      case FormPriorityEnum.low:
        return t.priority.low;
      case FormPriorityEnum.medium:
        return t.priority.medium;
      case FormPriorityEnum.high:
        return t.priority.high;
    }
  }
}
