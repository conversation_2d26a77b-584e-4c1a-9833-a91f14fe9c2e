import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum CompanyTypeEnum {
  commercial,
  individual,
}

extension CompanyTypeEnumExtension on CompanyTypeEnum {
  String displayName(BuildContext context) {
    switch (this) {
      case CompanyTypeEnum.commercial:
        return context.tr.companyType.commercial;
      case CompanyTypeEnum.individual:
        return context.tr.companyType.individual;
    }
  }
}
