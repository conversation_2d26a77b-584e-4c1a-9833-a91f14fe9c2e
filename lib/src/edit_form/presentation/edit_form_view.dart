import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/edit_form/application/edit_form_async_notifier.dart';
import 'package:smart_team_web/src/edit_form/application/forms_state.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/form_priority_enum_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/shared/providers/users_for_company_provider.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/date_time_picker/date_time_picker.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/question_builder/question_builder.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'widgets/create_form.dart';

@RoutePage(name: 'EditFormRoute')
class EditFormView extends StatefulHookConsumerWidget {
  const EditFormView({@pathParam required this.formTemplateId, super.key});

  final String formTemplateId;

  @override
  ConsumerState<EditFormView> createState() => _EditFormViewState();
}

class _EditFormViewState extends ConsumerState<EditFormView> {
  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();
    final editFormAsyncNotifier =
        ref.watch(editFormAsyncNotifierProvider(widget.formTemplateId));
    return Scaffold(
      body: SizedBox.expand(
        child: BaseAsyncProviderWidget<FormsState>(
          value: editFormAsyncNotifier,
          builder: (value) {
            return Align(
              alignment: Alignment.topCenter,
              child: Container(
                margin: const EdgeInsets.all(12),
                constraints: const BoxConstraints(maxWidth: 1200),
                child: PageView.builder(
                  controller: pageController,
                  itemCount: 3,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return _CreateForm(
                        formTemplateId: widget.formTemplateId,
                        onFormSubmitted: () {
                          pageController.animateToPage(
                            1,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeIn,
                          );
                        },
                      );
                    }
                    if (index == 1) {
                      return QuestionBuilder(
                        formTemplateId: widget.formTemplateId,
                        onPreviewPressed: () {
                          pageController.animateToPage(
                            2,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeIn,
                          );
                        },
                        onBackPressed: () {
                          pageController.animateToPage(
                            0,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeIn,
                          );
                        },
                      );
                    }
                    return QuestionBuilder(
                      formTemplateId: widget.formTemplateId,
                      isEditModeOn: false,
                      showSubmitButton: false,
                      onBackPressed: () {
                        pageController.animateToPage(
                          1,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeIn,
                        );
                      },
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
