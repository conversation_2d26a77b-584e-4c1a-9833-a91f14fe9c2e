part of '../edit_form_view.dart';

class _CreateForm extends StatefulHookConsumerWidget {
  const _CreateForm({
    required this.formTemplateId,
    required this.onFormSubmitted,
  });

  final String formTemplateId;
  final VoidCallback onFormSubmitted;

  @override
  ConsumerState<_CreateForm> createState() => _CreateFormState();
}

class _CreateFormState extends ConsumerState<_CreateForm> {
  late final EditFormAsyncNotifier _editFormAsyncNotifier;

  @override
  void initState() {
    super.initState();
    _editFormAsyncNotifier =
        ref.read(editFormAsyncNotifierProvider(widget.formTemplateId).notifier);
  }

  @override
  Widget build(BuildContext context) {
    final companyUsersAsyncProvider = ref.watch(usersForCompanyProvider);
    final formState =
        ref.watch(editFormAsyncNotifierProvider(widget.formTemplateId)).value!;
    final formNameController =
        useTextEditingController(text: formState.formTemplate.title);
    final formDescriptionController =
        useTextEditingController(text: formState.formTemplate.description);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      child: Column(
        spacing: 20,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr.form.createForm,
            style: ATextStyle.text18SemiBold,
          ),
          Row(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  spacing: 8,
                  children: [
                    CustomTextFormField(
                      headerText: context.tr.form.formName,
                      controller: formNameController,
                      onChanged: _editFormAsyncNotifier.updateTitle,
                    ),
                    DateTimePicker(
                      header: context.tr.common.startDate,
                      selectedDate: formState.formTemplate.startDate,
                      onSelected: _editFormAsyncNotifier.updateStartDate,
                    ),
                    BaseAsyncProviderWidget<PaginatedDataModel<User>>(
                      value: companyUsersAsyncProvider,
                      builder: (paginatedModel) {
                        return ANewDropdownFormField<User>(
                          header: context.tr.form.assignForm,
                          placeholder: context.tr.common.selectUsers,
                          itemList: paginatedModel.items,
                          allowMultipleSelection: true,
                          isDisabled: formState.formTemplate.isPublic,
                          onLoadMore: ref
                              .read(usersForCompanyProvider.notifier)
                              .fetchNextUsers,
                          itemBuilder: (value) =>
                              Text(value?.name ?? '', style: ATextStyle.text14),
                          selectedItems: formState.assignedUsers
                              .map((e) => e.user)
                              .toList(),
                          onSelected: _editFormAsyncNotifier.toggleAssignedUser,
                        );
                      },
                    ),
                    SwitchListTile(
                      value: formState.formTemplate.isPublic,
                      onChanged: (value) {
                        _editFormAsyncNotifier.setFormPublic(isPublic: value);
                      },
                      title: Text(
                        context.tr.form.openForm,
                        style: ATextStyle.text14,
                      ),
                      subtitle: Text(
                        context.tr.form.openFormDescription,
                        style: ATextStyle.text12,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  spacing: 8,
                  children: [
                    ANewDropdownFormField<FormPriorityEnum>(
                      header: context.tr.priorityLabel,
                      itemList: FormPriorityEnum.values,
                      selectedItems: [formState.formTemplate.priority],
                      itemBuilder: (value) => Text(
                        value?.getLocalizedName(context) ?? '',
                        style: ATextStyle.text14,
                      ),
                      onSelected: _editFormAsyncNotifier.updatePriority,
                      placeholder: '',
                    ),
                    DateTimePicker(
                      header: context.tr.common.endDate,
                      startDate: formState.formTemplate.startDate,
                      selectedDate: formState.formTemplate.endDate,
                      onSelected: _editFormAsyncNotifier.updateEndDate,
                    ),
                    CustomTextFormField(
                      headerText: context.tr.description,
                      maxLines: 3,
                      controller: formDescriptionController,
                      onChanged: _editFormAsyncNotifier.updateDescription,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Center(
            child: LoadingElevatedButton(
              width: 200,
              height: 40,
              text: context.tr.common.forward,
              suffix: const Icon(
                Icons.arrow_forward_ios_rounded,
                size: 16,
              ),
              onPressed: () async {
                widget.onFormSubmitted();
              },
            ),
          ),
        ],
      ),
    );
  }
}
