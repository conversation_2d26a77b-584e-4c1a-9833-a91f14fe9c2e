part of '../../admin_page.dart';

class _CompanyListDialog extends HookConsumerWidget {
  const _CompanyListDialog({this.onEditClicked});

  final void Function(Company, CompanyBilling)? onEditClicked;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companiesProvider = ref.watch(companiesFutureProvider);

    List<List<RowDataModel<dynamic>>> generateRowData(List<Company> companies) {
      return companies.map((company) {
        return [
          RowDataModel<String>(
            columnName: context.tr.customerId,
            value: company.id,
            cellBuilder: () => TextWidget(
              company.code!,
              style: ATextStyle.small,
            ),
          ),
          RowDataModel<String>(
            columnName: context.tr.companyName,
            value: company.name,
            cellBuilder: () => TextWidget(
              company.name,
              style: ATextStyle.small,
            ),
          ),
          RowDataModel(
            columnName: '',
            cellBuilder: () => LoadingElevatedButton(
              text: context.tr.edit,
              height: 30,
              width: 200,
              onPressed: () async {
                final companyBilling = await ref
                    .read(companyBillingRepositoryProvider)
                    .fetchCompanyBilling(company.id!);
                if (!context.mounted) return;
                if (companyBilling != null) {
                  await context.maybePop();
                  onEditClicked?.call(company, companyBilling);
                } else {
                  ref.read(toastManagerProvider).showToastErrorWithMessage(
                        context.tr.company.companyInfoNotFound,
                      );
                }
              },
            ),
          ),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<List<Company>>(
      value: companiesProvider,
      loadingWidget: const Placeholder(),
      builder: (companies) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SmartTeamDataTable(
            columns: [
              TableColumnModel(
                columnName: context.tr.customerId,
                filterable: false,
                sortable: false,
                width: 200,
              ),
              TableColumnModel(
                columnName: context.tr.companyName,
              ),
              const TableColumnModel(
                columnName: '',
                width: 150,
                filterable: false,
                sortable: false,
              ),
            ],
            rowData: generateRowData(companies),
            initialRowsPerPage: 10,
          ),
        );
      },
    );
  }
}
