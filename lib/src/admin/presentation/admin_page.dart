import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/core/managers/global_state_manager/global_state_manager.dart';
import 'package:smart_team_web/core/router/auto_router.gr.dart';
import 'package:smart_team_web/src/admin/application/add_company_provider.dart';
import 'package:smart_team_web/src/admin/application/admin_dashboard_provider.dart';
import 'package:smart_team_web/src/admin/application/companies_provider.dart';
import 'package:smart_team_web/src/devices/application/new_device_provider.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/main/application/main_provider.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/enums/panel_user_role_enum.dart';
import 'package:smart_team_web/src/shared/enums/regex_type.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/decimal_text_input_formetter.dart';
import 'package:smart_team_web/src/shared/extensions/string_extensions.dart';
import 'package:smart_team_web/src/shared/managers/loading_overlay_manager/loading_overlay_manager.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/toast/toast_manager.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/app_dialog/premade_dialogs/feature_dialog.dart';

import 'package:smart_team_web/src/widgets/app_shimmer.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/button/press_icon_button.dart';
import 'package:smart_team_web/src/widgets/form_fields/dropdown_formfield.dart';
import 'package:smart_team_web/src/widgets/hover_builder.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/popup/dynamic_dialog.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';
import 'package:smart_team_web/src/widgets/text_form_field/date_time_picker_field.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

part 'components/left_container/admin_left_container.dart';
part 'components/left_container/companies_list.dart';

part 'components/dialogs/company_list_dialog.dart';
// part 'components/dialogs/gps_devices_list_dialog.dart';
part 'components/dialogs/users_list_dialog.dart';
part 'components/dialogs/reports_list_dialog.dart';

part 'widgets/add_new_company_widgets.dart';
part 'widgets/admin_menu_item.dart';
part 'widgets/settings_widgets.dart';
part 'widgets/add_new_vehicle_widgets.dart';
// part 'widgets/add_new_gps_device_widgets.dart';
part 'widgets/components/gps_device_form_left_column.dart';
part 'widgets/components/gps_device_form_right_column.dart';
part 'widgets/add_new_users_widgets.dart';

@RoutePage(name: 'AdminRoute')
class AdminPage extends HookConsumerWidget {
  const AdminPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCompany = ref.watch(selectedCompanyProvider);

    Future<void> addCompany({
      Company? company,
      CompanyBilling? companyBilling,
    }) async {
      showAddDialog(
        context,
        title: context.tr.addNewCustomer,
        fields: AddNewCompanyWidgets(
          company: company,
          companyBilling: companyBilling,
        ),
        onSave: (context) async {
          ref
              .read(loadingOverlayManagerProvider.notifier)
              .toggleLoadingOverlay();
          var isOperationSuccess = false;
          if (company == null) {
            isOperationSuccess =
                await ref.read(addCompanyProvider).createCompany();
          } else {
            isOperationSuccess =
                await ref.read(addCompanyProvider).updateCompany();
          }

          ref
              .read(loadingOverlayManagerProvider.notifier)
              .toggleLoadingOverlay();
          if (context.mounted) {
            await context.maybePop();
          }

          if (isOperationSuccess && context.mounted) {
            ref.read(toastManagerProvider).showToast(
                  company == null
                      ? context.tr.company.companyCreateSuccessful
                      : context.tr.company.companyUpdateSuccessful,
                );
          } else {
            if (context.mounted) {
              ref.read(toastManagerProvider).showToastErrorWithMessage(
                    company == null
                        ? context.tr.company.companyCouldNotBeCreated
                        : context.tr.company.companyCouldNotBeUpdated,
                  );
            }
          }
        },
      );
    }

    // Future<void> addGPSDevice({GPSDeviceModel? device}) async {
    //   showAddDialog(
    //     context,
    //     title: device == null
    //           ? context.tr.common.addGpsDevice
    //         : 'context.tr.common.editGpsDevice
    //     fields: AddNewGPSDeviceWidgets(
    //       device: device,
    //     ),
    //     onSave: (context) async {
    //       ref
    //           .read(loadingOverlayManagerProvider.notifier)
    //           .toggleLoadingOverlay();
    //       bool success;
    //       if (device == null) {
    //         success = await ref.read(newGpsDeviceProvider).createDevice();
    //       } else {
    //         success = await ref.read(newGpsDeviceProvider).updateDevice();
    //       }
    //       ref
    //           .read(loadingOverlayManagerProvider.notifier)
    //           .toggleLoadingOverlay();
    //       if (context.mounted) await context.maybePop();
    //       ref.read(toastManagerProvider).showToast(
    //             success
    //                 ? device == null
    //                     ? context.tr.common.createGpsDeviceSuccessful
    //                     : context.tr.common.updateGpsDeviceSuccessful
    //                 : device == null
    //                     ? context.tr.common.createGpsDeviceFailed
    //                     : context.tr.common.updateGpsDeviceFailed,
    //           );
    //     },
    //   );
    // }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 32),
      child: Row(
        spacing: 16,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Expanded(
            child: _AdminLeftContainer(),
          ),
          Expanded(
            flex: 4,
            child: Column(
              spacing: 16,
              children: [
                Row(
                  spacing: 16,
                  children: [
                    AdminMenuItem(
                      icon: Icons.business_rounded,
                      title: context.tr.company.companies,
                      description: context.tr.company.companyOperations,
                      showOpen: false,
                      onListSelected: () {
                        showAddDialog(
                          context,
                          title: context.tr.company.companyList,
                          fields: _CompanyListDialog(
                            onEditClicked: (company, companyBilling) {
                              addCompany(
                                company: company,
                                companyBilling: companyBilling,
                              );
                            },
                          ),
                          showBottomActions: false,
                        );
                      },
                      onAddSelected: addCompany,
                    ),
                    AdminMenuItem(
                      icon: Icons.settings,
                      title: context.tr.vehicle.vehicleModels,
                      description: context.tr.vehicle.vehicleModelOperations,
                      showOpen: false,
                      onListSelected: () {
                        AppDialog.show<void>(
                          context: context,
                          width: context.width * .95,
                          height: context.height * .95,
                          title: context.tr.vehicle.vehicleList,
                          child: TableDialog(
                            columns: [
                              context.tr.vehicle.vehicleType,
                              context.tr.brand,
                              context.tr.model,
                              context.tr.vehicle.co2EmissionValue,
                            ],
                            data: const [],
                          ),
                        );
                      },
                      onAddSelected: () {
                        AppDialog.show<void>(
                          context: context,
                          title: context.tr.vehicle.addVehicle,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const AddNewVehicleWidgets(),
                        );
                      },
                    ),
                    AdminMenuItem(
                      icon: Icons.settings,
                      title: context.tr.settings,
                      description: '${context.tr.settings}.',
                      showAdd: false,
                      showList: false,
                      onOpenSelected: () {
                        showAddDialog(
                          context,
                          title: context.tr.settings,
                          fields: SizedBox(
                            height: context.height * 1.1,
                            child: const SettingsWidgets(),
                          ),
                        );
                      },
                    ),
                    // AdminMenuItem(
                    //   icon: Icons.settings,
                    //   title: context.tr.common.gpsDevices,
                    //   description: context.tr.common.gpsDeviceOperations,
                    //   showOpen: false,

                    //   onListSelected: () {
                    //     showAddDialog(
                    //       context,
                    //       title: context.tr.common.gpsDeviceList,
                    //       fields: _GPSDevicesListDialog(
                    //         onEditClicked: (device) {
                    //           addGPSDevice(device: device);
                    //         },
                    //       ),
                    //       showBottomActions: false,
                    //     );
                    //   },
                    //   onAddSelected: addGPSDevice,
                    //   // onAddSelected: () {
                    //   //   AppDialog.show<void>(
                    //   //     context: context,
                    //   //     title: context.tr.common.addGpsDevice,
                    //   //     width: context.width * .95,
                    //   //     height: context.height * .95,
                    //   //     child: const AddNewGPSDeviceWidgets(),
                    //   //   );
                    //   // },
                    // ),
                  ],
                ),
                Row(
                  spacing: 16,
                  children: [
                    AdminMenuItem(
                      icon: Icons.settings,
                      title: context.tr.company.companyPanel,
                      description: '',
                      showAdd: false,
                      showList: false,
                      showOpen: selectedCompany.value != null,
                      onOpenSelected: () {
                        if (selectedCompany.value == null) {
                          ref
                              .read(toastManagerProvider)
                              .showToastErrorWithMessage(
                                  context.tr.company.pleaseSelectCompany);
                          return;
                        }
                        context.navigateTo(const DashboardRoute());
                      },
                    ),
                    AdminMenuItem(
                      icon: Icons.settings,
                      title: context.tr.user,
                      description: context.tr.userOperations,
                      showOpen: false,
                      onListSelected: () {
                        showAddDialog(
                          context,
                          title: context.tr.userList,
                          fields: _UsersListDialog(
                            onEditClicked: (company, companyBilling) {
                              addCompany(
                                company: company,
                                companyBilling: companyBilling,
                              );
                            },
                          ),
                          showBottomActions: false,
                        );
                      },
                      onAddSelected: () {
                        AppDialog.show<void>(
                          context: context,
                          title: context.tr.addUser,
                          width: context.width * .95,
                          height: context.height * .95,
                          child: const AddNewUsersWidgets(),
                        );
                      },
                    ),
                    AdminMenuItem(
                      icon: Icons.settings,
                      title: context.tr.reports,
                      description: context.tr.reportOperations,
                      showOpen: false,
                      showAdd: false,
                      onListSelected: () {
                        showAddDialog(
                          context,
                          title: context.tr.reportsList,
                          fields: _ReportsListDialog(
                            onEditClicked: (company, companyBilling) {
                              addCompany(
                                company: company,
                                companyBilling: companyBilling,
                              );
                            },
                          ),
                          showBottomActions: false,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
