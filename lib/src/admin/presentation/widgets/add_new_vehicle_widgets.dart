part of '../admin_page.dart';

class AddNewVehicleWidgets extends HookConsumerWidget {
  const AddNewVehicleWidgets({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final groups = ref.watch(groupsProvider);

    final selectedGroup = useState<String?>(null);
    final selectedColor = useState<Color>(AColor.primaryColor);

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 16,
          children: [
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: ANewDropdownFormField<String>(
                    header: context.tr.vehicle.vehicleType,
                    placeholder: context.tr.select,
                    itemList: groups,
                    selectedItems: selectedGroup.value == null
                        ? []
                        : [selectedGroup.value!],
                    onSelected: (value) {
                      selectedGroup.value = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.tr.fieldRequired;
                      }
                      return null;
                    },
                  ),
                ),
                Expanded(
                  child: CustomTextFormField(
                    headerText: context.tr.brand,
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    onChanged: (value) {},
                    validator: (value) => value.isValidBrand(context),
                    regexType: RegexType.brand,
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16,
              children: [
                Expanded(
                  child: CustomTextFormField(
                    headerText: context.tr.model,
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    onChanged: (value) {},
                    validator: (value) => value.isValidModel(context),
                    regexType: RegexType.model,
                  ),
                ),
                Expanded(
                  child: CustomTextFormField(
                    headerText: context.tr.vehicle.co2EmissionValue,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    inputFormatters: [
                      DecimalTextInputFormatter(),
                    ],
                    onChanged: (value) {},
                    validator: (value) => value.isValidDecimal(context),
                    regexType: RegexType.decimal,
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                LoadingElevatedButton(
                  height: 45,
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) {
                      ref
                          .read(toastManagerProvider)
                          .showToast(context.tr.fillAllFields);
                      return;
                    }
                    if (formKey.currentState!.validate()) {}
                  },
                  text: context.tr.save,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
