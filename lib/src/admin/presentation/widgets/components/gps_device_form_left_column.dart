part of '../../admin_page.dart';

class GPSDeviceFormLeftColumn extends HookConsumerWidget {
  const GPSDeviceFormLeftColumn({
    required this.deviceIdController,
    required this.imeiController,
    required this.odometerController,
    required this.selectedOnlineStatus,
    required this.onlineOffline,
    super.key,
  });

  final TextEditingController deviceIdController;
  final TextEditingController imeiController;
  final TextEditingController odometerController;
  final ValueNotifier<String?> selectedOnlineStatus;
  final List<String> onlineOffline;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      spacing: 16,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.common.deviceId,
            controller: deviceIdController,
            hintText: '',
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
            ],
            regexType: RegexType.id,
            suffixIcon: Padding(
              padding: const EdgeInsets.all(1),
              child: Container(
                decoration: const BoxDecoration(
                  color: AColor.mercury,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(2),
                    bottomRight: Radius.circular(2),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_up),
                      onTap: () {
                        final currentValue = int.tryParse(
                              deviceIdController.text,
                            ) ??
                            0;
                        if (currentValue < 999) {
                          deviceIdController.text =
                              (currentValue + 1).toString();
                        }
                      },
                    ),
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_down),
                      onTap: () {
                        final currentValue = int.tryParse(
                              deviceIdController.text,
                            ) ??
                            0;
                        deviceIdController.text = (currentValue - 1).toString();
                      },
                    ),
                  ],
                ),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return context.tr.validation.deviceIdAreaCantBeEmpty;
              }
              return null;
            },
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.common.imei,
            controller: imeiController,
            hintText: '',
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(15),
            ],
            regexType: RegexType.id,
            suffixIcon: Padding(
              padding: const EdgeInsets.all(1),
              child: Container(
                decoration: const BoxDecoration(
                  color: AColor.mercury,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(2),
                    bottomRight: Radius.circular(2),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_up),
                      onTap: () {
                        final currentValue =
                            int.tryParse(imeiController.text) ?? 0;
                        if (currentValue < 999) {
                          imeiController.text = (currentValue + 1).toString();
                        }
                      },
                    ),
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_down),
                      onTap: () {
                        final currentValue =
                            int.tryParse(imeiController.text) ?? 0;
                        imeiController.text = (currentValue - 1).toString();
                      },
                    ),
                  ],
                ),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return context.tr.validation.imeiCantBeEmpty;
              }
              return null;
            },
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.firmware,
            hintText: '',
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onChanged: (value) {},
            regexType: RegexType.name,
          ),
        ),
        ANewDropdownFormField<String>(
          header: context.tr.currentStatus,
          placeholder: context.tr.select,
          itemList: onlineOffline,
          selectedItems: selectedOnlineStatus.value == null
              ? []
              : [selectedOnlineStatus.value!],
          onSelected: (value) {
            selectedOnlineStatus.value = value;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.fieldRequired;
            }
            return null;
          },
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: DateTimePickerField(
            header: context.tr.lastDataDateAndTime,
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.odometer,
            controller: odometerController,
            hintText: '',
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
            ],
            regexType: RegexType.id,
            suffixIcon: Padding(
              padding: const EdgeInsets.all(1),
              child: Container(
                decoration: const BoxDecoration(
                  color: AColor.mercury,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(2),
                    bottomRight: Radius.circular(2),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_up),
                      onTap: () {
                        final currentValue = int.tryParse(
                              odometerController.text,
                            ) ??
                            0;
                        if (currentValue < 999) {
                          odometerController.text =
                              (currentValue + 1).toString();
                        }
                      },
                    ),
                    PressIconButton(
                      icon: const Icon(Icons.arrow_drop_down),
                      onTap: () {
                        final currentValue = int.tryParse(
                              odometerController.text,
                            ) ??
                            0;
                        odometerController.text = (currentValue - 1).toString();
                      },
                    ),
                  ],
                ),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return context.tr.validation.odometerCantBeEmpty;
              }
              return null;
            },
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.vehicle.plate,
            hintText: '',
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onChanged: (value) {},
            regexType: RegexType.name,
          ),
        ),
        Container(
          decoration: CommonDecorations.containerDecoration(),
          child: CustomTextFormField(
            headerText: context.tr.vehicle.co2EmissionValue,
            hintText: '',
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onChanged: (value) {},
            regexType: RegexType.name,
          ),
        ),
      ],
    );
  }
}
