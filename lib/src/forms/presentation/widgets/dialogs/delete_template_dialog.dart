part of '../../forms_view.dart';

class _DeleteTemplateDialog extends HookConsumerWidget {
  const _DeleteTemplateDialog({
    required this.formTitle,
  });

  final String formTitle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 24,
      children: [
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(text: "'$formTitle'", style: ATextStyle.text16SemiBold.copyWith(color: AColor.primaryColor)),
              TextSpan(text: ' başlıklı formu silmek istediğinize emin misiniz?'.hardcoded, style: ATextStyle.text16SemiBold),
            ],
          ),
        ),
        Row(
          spacing: 12,
          children: [
            Expanded(
              child: LoadingElevatedButton(
                onPressed: context.maybePop,
                height: 30,
                text:  context.tr.cancel,
                textColor: AColor.black,
                backgroundColor: Colors.white,
              ),
            ),
            Expanded(
              child: LoadingElevatedButton(
                height: 30,
                onPressed: () => context.maybePop(true),
                text:  context.tr.delete,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
