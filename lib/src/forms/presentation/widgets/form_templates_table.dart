part of '../forms_view.dart';

class _FormTemplatesTable extends HookConsumerWidget {
  const _FormTemplatesTable({required this.formTemplatesAndUsers, super.key});

  final List<FormTemplateAndUsers> formTemplatesAndUsers;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tableColumnNames = [
      context.tr.form.formName,
      context.tr.priorityLabel,
      context.tr.worker,
      context.tr.common.open,
      context.tr.common.startDate,
      context.tr.common.endDate,
      context.tr.common.creationDate,
      context.tr.description,
      context.tr.answers,
    ];

    List<List<RowDataModel<dynamic>>> getRowData() {
      return formTemplatesAndUsers.map((formTemplateAndUser) {
        return [
          RowDataModel<String>(
            columnName: tableColumnNames.first,
            value: formTemplateAndUser.formTemplate.title,
          ),
          RowDataModel<String>(
            columnName: tableColumnNames[1],
            value: formTemplateAndUser.formTemplate.priority.name,
          ),
          RowDataModel<List<String>>(
            columnName: tableColumnNames[2],
            value: formTemplateAndUser.users,
            cellBuilder: () {
              if (formTemplateAndUser.formTemplate.isPublic) {
                return const Text('Herkese Açık');
              }
              if (formTemplateAndUser.users.isEmpty) {
                return const Text('-');
              }
              final isShowAllVisible = formTemplateAndUser.users.length > 3;
              final userNames = formTemplateAndUser.users
                  .take(3)
                  .map((user) => user)
                  .join(', ');
              return Row(
                spacing: 12,
                children: [
                  Flexible(
                    child: Text(
                      userNames,
                      style: ATextStyle.text14,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (isShowAllVisible)
                    HookBuilder(
                      builder: (context) {
                        final isPortalOpen = useState(false);
                        return PortalTarget(
                          visible: isPortalOpen.value,
                          anchor: const Aligned(
                            follower: Alignment.topCenter,
                            target: Alignment.bottomCenter,
                            shiftToWithinBound: AxisFlag(y: true, x: true),
                            //offset: additionalOffset,
                          ),
                          portalFollower: Container(
                            width: 300,
                            height: 300,
                            padding:
                                const EdgeInsets.all(24).copyWith(right: 16),
                            decoration: const BoxDecoration(
                              color: AColor.white,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        context.tr.personnel.selectedEmployees,
                                        style: ATextStyle.text18SemiBold,
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        isPortalOpen.value = false;
                                      },
                                      icon: const Icon(Icons.close),
                                    ),
                                  ],
                                ),
                                Expanded(
                                  child: ListView.separated(
                                    itemCount: formTemplateAndUser.users.length,
                                    separatorBuilder: (context, index) {
                                      return const Gap(4);
                                    },
                                    itemBuilder: (context, index) {
                                      return Text(
                                        formTemplateAndUser.users[index],
                                        style: ATextStyle.text14,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          child: InkWell(
                            onTap: () {
                              isPortalOpen.value = true;
                            },
                            child: Text(
                              context.tr.common.viewAll,
                              style: ATextStyle.text12.copyWith(
                                color: AColor.primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                ],
              );
            },
          ),
          RowDataModel<bool>(
            columnName: tableColumnNames[3],
            value: formTemplateAndUser.formTemplate.isPublic,
          ),
          RowDataModel<DateTime?>(
            columnName: tableColumnNames[4],
            value: formTemplateAndUser.formTemplate.startDate,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  formTemplateAndUser.formTemplate.startDate,
                  format: DTFormat.dayMonthYear,
                ),
                style: ATextStyle.text14,
              );
            },
          ),
          RowDataModel<DateTime?>(
            columnName: tableColumnNames[5],
            value: formTemplateAndUser.formTemplate.endDate,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  formTemplateAndUser.formTemplate.endDate,
                  format: DTFormat.dayMonthYear,
                ),
                style: ATextStyle.text14,
              );
            },
          ),
          RowDataModel<DateTime?>(
            columnName: tableColumnNames[6],
            value: formTemplateAndUser.formTemplate.createdAt,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  formTemplateAndUser.formTemplate.createdAt,
                  format: DTFormat.dayMonthYear,
                ),
                style: ATextStyle.text14,
              );
            },
          ),
          RowDataModel<String>(
            columnName: tableColumnNames[7],
            value: formTemplateAndUser.formTemplate.description,
          ),
          RowDataModel<void>(
            columnName: tableColumnNames[8],
            cellBuilder: () {
              return Row(
                children: [
                  if (formTemplateAndUser.hasSubmission)
                    IconButton(
                      onPressed: () {
                        AppDialog.show<void>(
                          context: context,
                          title:
                              '${formTemplateAndUser.formTemplate.title} ${context.tr.common.responsesTo}',
                          child: _FormAnswersDialog(
                              formTemplate: formTemplateAndUser.formTemplate),
                        );
                      },
                      icon: const Icon(Icons.search),
                    ),
                  const Spacer(),
                  if (formTemplateAndUser.formTemplate.isPublic)
                    IconButton(
                      tooltip: context.tr.form.goToForm,
                      onPressed: () {
                        final baseUrl =
                            ref.read(urlManagerProvider).getBaseUrlWithUri();
                        final formUrl =
                            '$baseUrl/view-form/${formTemplateAndUser.formTemplate.id}';
                        ref.read(urlManagerProvider).goToLink(formUrl);
                      },
                      color: AColor.primaryColor,
                      icon: const Icon(Icons.language_outlined),
                    ),
                  IconButton(
                    tooltip: context.tr.common.copy,
                    onPressed: () {
                      context.navigateTo(
                        EditFormRoute(
                          formTemplateId: formTemplateAndUser.formTemplate.id!,
                        ),
                      );
                    },
                    color: AColor.primaryColor,
                    icon: const Icon(Icons.copy_rounded),
                  ),
                  IconButton(
                    tooltip: context.tr.delete,
                    onPressed: () async {
                      final isDeleteConfirmed = await AppDialog.show<bool?>(
                        context: context,
                        width: 400,
                        padding: const EdgeInsets.all(24),
                        borderRadius: BorderRadius.circular(12),
                        barrierDismissible: false,
                        showHeader: false,
                        showCloseButton: false,
                        child: _DeleteTemplateDialog(
                          formTitle: formTemplateAndUser.formTemplate.title,
                        ),
                      );
                      if (isDeleteConfirmed != null &&
                          isDeleteConfirmed &&
                          context.mounted) {
                        final isDeleted = await ref
                            .read(formTemplatesNotifierProvider.notifier)
                            .deleteFormTemplate(
                                formTemplateAndUser.formTemplate);
                        if (context.mounted) {
                          ref.read(toastManagerProvider).showToast(
                                isDeleted
                                    ? context.tr.form.formDeletedSuccessfully
                                    : context.tr.form.formCouldNotBeDeleted,
                              );
                        }
                      }
                    },
                    color: AColor.primaryColor,
                    icon: const Icon(Icons.delete_outline),
                  ),
                ],
              );
            },
          ),
        ];
      }).toList();
    }

    return SmartTeamDataTable(
      columns: [
        TableColumnModel(columnName: tableColumnNames.first),
        TableColumnModel(columnName: tableColumnNames[1], width: 100),
        TableColumnModel(columnName: tableColumnNames[2], width: 300),
        TableColumnModel(columnName: tableColumnNames[3], width: 100),
        TableColumnModel(columnName: tableColumnNames[4], width: 150),
        TableColumnModel(columnName: tableColumnNames[5], width: 150),
        TableColumnModel(columnName: tableColumnNames[6], width: 150),
        TableColumnModel(columnName: tableColumnNames[7]),
        TableColumnModel(
          columnName: tableColumnNames[8],
          width: 200,
          filterable: false,
          sortable: false,
        ),
      ],
      rowData: getRowData(),
    );
  }
}
