{"appTitle": "Smart Team Web", "start": "Başla", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Hizmet Şartları</link> ve <link href=\"privacyPolicy\">Gizlilik Politikası</link>", "registerAgreement": "<link>Üyelik Sözleşmesi</link>, <link>Veri Koruma ve İş Politikası</link>, <link>Müşteri Açıklama Metni</link>, <link>Gizlilik ve Çerez Politikası</link>'nı okuduğumu ve kabul ettiğimi onaylıyorum.", "name": "Ad", "surname": "Soyad", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "search": "Ara", "add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "action": "İşlem", "documentLabel": "Belge", "save": "<PERSON><PERSON>", "cancel": "İptal", "confirm": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "ok": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "warning": "Uyarı", "info": "<PERSON><PERSON><PERSON>", "required": "Zorun<PERSON>", "optional": "İsteğe Bağlı", "select": "Seçiniz...", "all": "Tümü", "none": "Hiç<PERSON>i", "online": "Çevrimiçi", "offline": "Çevrimdışı", "admin": "Yönetici", "user": "Kullanıcı", "userOperations": "Kullanıcı İşlemleri", "userList": "Kullanıcı Listesi", "addUser": "Kullanıcı Ekle", "type": "Tip", "title": "Başlık", "group": "Grup", "status": "Durum", "open": "Aç", "list": "Listele", "addNew": "<PERSON><PERSON>", "pick": "Seç", "selectColor": "Renk Seç", "fieldRequired": "<PERSON>", "noDataFound": "<PERSON><PERSON> bulunamadı", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "page": "Say<PERSON>", "record": "kayıt", "waiting": "Bekliyor", "pending": "Beklemede", "accepted": "Kabul edildi", "declined": "Reddedildi", "done": "Tamamlandı", "inProgress": "<PERSON><PERSON>", "waitingForInfo": "<PERSON><PERSON><PERSON>", "cancelled": "İptal Edildi", "overdue": "Süresi Geçmiş", "passive": "<PERSON><PERSON><PERSON>", "potential": "Po<PERSON><PERSON><PERSON><PERSON>", "answers": "Yanıtlar", "priorityLabel": "Öncelik", "priority": {"low": "Düşük", "medium": "Orta", "high": "<PERSON><PERSON><PERSON><PERSON>"}, "taskType": {"visit": "<PERSON><PERSON><PERSON>", "collection": "Tahsilat", "service": "<PERSON><PERSON>", "onlineMeeting": "Online Toplantı", "phoneCall": "Telefon Görüşmesi"}, "device": {"brands": {"teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>"}, "models": {"modelA": "Model A", "modelB": "Model B", "modelC": "Model C"}, "simOperators": {"vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom"}, "addDevice": "<PERSON><PERSON><PERSON>", "deviceList": "<PERSON><PERSON><PERSON>", "activationDate": "Aktivasyon <PERSON>"}, "vehicle": {"addVehicle": "<PERSON><PERSON>", "vehicleList": "<PERSON><PERSON>", "vehicleBrand": "<PERSON><PERSON>", "vehicleModel": "<PERSON><PERSON>", "vehicleType": "<PERSON><PERSON>", "vehicleModels": "<PERSON><PERSON>", "vehicleModelOperations": "Araç Model İşlemleri", "co2EmissionValue": "CO2 - <PERSON><PERSON><PERSON>", "plate": "<PERSON><PERSON><PERSON>", "brands": {"mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi"}}, "customer": {"customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addCustomer": "Müşter<PERSON>", "customerA": "A Müşterisi", "customerB": "B Müşterisi", "customerC": "C Müşterisi", "phoneNumber": "Telefon Numarası", "address": "<PERSON><PERSON>", "openAddress": "Açık Adres", "findMyLocation": "Konumumu <PERSON>", "fillAllFields": "<PERSON><PERSON><PERSON> alanları doldurunuz"}, "groups": {"groupA": "Grup A", "groupB": "Grup B", "groupC": "Grup C", "groupD": "Grup D"}, "dashboard": "Panel", "dashboardDetails": {"todoList": "Yapılacaklar Listesi", "instantMovements": "<PERSON><PERSON><PERSON><PERSON>", "instantStoppage": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"addForm": "Form Ekle", "openForm": "Açık Form", "formName": "Form İsmi", "assignForm": "<PERSON><PERSON>", "goToForm": "Forma G<PERSON>", "openFormDescription": "Açık form, herkes tarafından görülebilir ve kişilere atanamaz", "deleteFormConfirm": "'{title}' ba<PERSON>lıklı formu silmek istediğinize emin misiniz?", "requiredQuestion": "* Zorunlu soruyu belirtir", "addOption": "Seçenek ekle", "createForm": "Form Oluştur", "formDeletedSuccessfully": "Form başar<PERSON><PERSON> silindi", "formCouldNotBeDeleted": "Form silinirken bir hata oluştu", "updateForm": "<PERSON><PERSON>"}, "area": {"areaEntry": "<PERSON><PERSON>", "areaWaiting": "<PERSON><PERSON>", "areaExit": "Alandan <PERSON>", "editArea": "Alanı <PERSON>", "areaName": "<PERSON>", "areaType": "<PERSON>"}, "emergency": {"emergencyContactName": "Acil Durum Adı-Soyadı", "emergencyPhone": "Acil Durum Telefonu", "emergencyContact": "<PERSON>cil Durumda Aranacak <PERSON>i"}, "validation": {"linkCouldNotOpen": "Link açılamadı", "phoneNumberExample": "+90 555 555 55 55", "phoneNumberShort": "555 555 55 55", "pleaseSelectValid": "Lütfen geçerli bir seçenek seçiniz", "thisFieldRequired": "<PERSON><PERSON> <PERSON><PERSON>", "invalidNumberFormat": "Geçersiz sayı formatı", "pleaseEnterValidNumber": "Lütfen geçerli bir sayı giriniz", "pleaseEnterValidBrand": "Lütfen geçerli bir marka giriniz", "pleaseEnterValidModel": "Lütfen geçerli bir model giriniz", "pleaseFillRequiredFields": "Lütfen zorunlu alanları doldurun", "pleaseSelectRole": "Lütfen rol seçiniz", "pleaseSelectAtLeastOneDevice": "Lütfen en az bir cihaz seçin", "pleaseSelectPersonnel": "Lütfen bir personel seçin", "pleaseAddAtLeastOneQuestion": "Lütfen en az bir soru ekleyiniz", "fillAllFields": "<PERSON><PERSON><PERSON> alanları doldurunuz", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "endDateCannotBeBeforeStartDate": "Bitiş tarihi ba<PERSON>langıç tarihinden önce olamaz", "deviceIdAreaCantBeEmpty": "Cihaz ID alanı boş kalamaz", "imeiCantBeEmpty": "IMEI alanı boş kalamaz", "odometerCantBeEmpty": "Odometre alanı boş kalamaz", "simCardNoCantBeEmpty": "SIM Kart No alanı boş kalamaz", "startTimeCannotBeAfterEndTime": "Başlangıç zamanı bitiş zamanından önce olamaz"}, "copyright": "Akıllı Konum Teknolojileri A.Ş. © 2025", "common": {"update": "<PERSON><PERSON><PERSON><PERSON>", "create": "Oluştur", "reset": "Sıfırla", "clear": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAll": "Tümünü Seç", "expandAll": "Tümünü genişlet", "collapseAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAll": "Tümünü <PERSON>", "filter": "Filtrele", "clearAllFilters": "<PERSON><PERSON><PERSON>", "copy": "Kopyala", "preview": "<PERSON><PERSON><PERSON><PERSON>", "detail": "Detay", "details": "Detaylar", "back": "<PERSON><PERSON>", "forward": "İleri", "inspect": "<PERSON><PERSON><PERSON>", "operation": "İşlem", "operations": "İşlemler", "result": "<PERSON><PERSON><PERSON>", "noResultFound": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "loadingData": "<PERSON><PERSON>...", "pleaseWait": "Lütfen bekleyiniz...", "tryAgain": "<PERSON>ir daha dene", "areYouSure": "Emin misin?", "deleteConfirmation": "<PERSON><PERSON><PERSON>", "updateSuccessful": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarılı", "createSuccessful": "Oluşturma başarılı", "deleteSuccessful": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>ı", "operationFailed": "İşlem başarısız", "loginSuccessful": "<PERSON><PERSON><PERSON> başarılı", "loginFailed": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "mailSent": "Mail gönderildi", "mailCouldNotBeSent": "Mail gönderilemedi", "linkExpired": "Link süresi doldu. Lütfen bir daha mail gönderiniz.", "passwordUpdated": "<PERSON><PERSON><PERSON> g<PERSON>. Yeni şifreniz ile giriş yapabilirsiniz.", "passwordCouldNotBeUpdated": "<PERSON><PERSON><PERSON>", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>", "enterYourEmailToResetPassword": "Şifrenizi yenilemek için mail adresinizi giriniz:", "sendMail": "Mail Gönder", "mail": "Mail", "findMyLocation": "Konumumu bul", "selectOption": "Seçiniz...", "expenseType": "<PERSON><PERSON><PERSON><PERSON>", "expenseDate": "<PERSON><PERSON><PERSON><PERSON>", "imei": "IMEI", "kilometer": "Kilometre", "simOperator": "SIM Operatörü", "simCardNumber": "SIM Kart No", "deviceId": "Cihaz ID", "vehicleBrand": "<PERSON><PERSON>", "vehicleModel": "<PERSON><PERSON>", "serverKey": "Server Key", "approvalDate": "<PERSON><PERSON>", "permitType": "İzin Türü", "permitCalendar": "<PERSON><PERSON>", "permitDuration": "<PERSON><PERSON>", "permitStart": "İzin Başlangıç", "permitEnd": "İzin Bitiş", "viewDocument": "Belge Görüntüle", "addNewJob": "<PERSON><PERSON>", "formSubmitter": "<PERSON><PERSON> ka<PERSON>n", "submissionDate": "<PERSON><PERSON><PERSON><PERSON>", "gpsDevices": "GPS Cihazları", "gpsDeviceOperations": "GPS Cihaz İşlemleri", "gpsDeviceList": "GPS Cihaz Listesi", "addGpsDevice": "GPS Cihaz Ekle", "editGpsDevice": "GPS Cihaz Düzenle", "createGpsDeviceSuccessful": "GPS Cihaz başarıyla oluşturuldu", "updateGpsDeviceSuccessful": "GPS Cihaz başarıyla güncellendi", "createGpsDeviceFailed": "GPS Cihaz oluşturulamadı", "updateGpsDeviceFailed": "GPS Cihaz güncellenemedi", "noResultsFound": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "searchPerson": "<PERSON><PERSON><PERSON> ara...", "assignedUsers": "<PERSON><PERSON><PERSON>: ", "required": "Zorun<PERSON>", "selectFieldType": "Select field type", "question": "Question", "addOption": "Add option", "addQuestion": "<PERSON><PERSON>", "all": "Tümü", "dailyMeetings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instantMovements": "<PERSON><PERSON><PERSON><PERSON>", "instantStop": "<PERSON><PERSON><PERSON><PERSON>", "mapScreen": "Harita <PERSON>", "editJob": "<PERSON><PERSON><PERSON>", "todoList": "Todo List", "pleaseSelectAtLeastOneDevice": "Lütfen en az bir cihaz seçin", "deactivateActivate": "Deaktif/Aktif", "smartLocationTechnologies": "Akıllı Konum Teknolojileri A.Ş. © 2025", "editPersonnel": "<PERSON><PERSON>", "personnelManagement": "<PERSON><PERSON>", "pleaseEnterValidNumber": "Lütfen geçerli bir sayı giriniz", "pleaseFillRequiredFields": "Lütfen zorunlu alanları doldurun", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "creationDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectUsers": "Kişileri seçiniz", "open": "Açık", "responsesTo": "Yanıtları"}, "companyType": {"commercial": "<PERSON><PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>"}, "addNewCustomer": "<PERSON><PERSON>", "customerId": "Müşteri Id", "customerName": "Müşteri Adı", "companyName": "Firma Adı", "contactName": "Kontak Adı", "authorizedName": "Yet<PERSON><PERSON> Adı", "authorizedEmail": "<PERSON><PERSON><PERSON>", "contactGSM": "Kontak GSM", "authorizedGSM": "Yetkili GSM", "contactEmail": "Kontak Email", "taxOffice": "<PERSON><PERSON><PERSON>", "taxNumber": "Vergi No", "customerPhone": "Müşteri Tel", "webAddress": "Web Adresi", "userName": "Kullanıcı Adı", "password": "Şifre", "nameSurname": "Ad Soyad", "email": "Email", "phone": "Telefon", "newUser": "<PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userDevice": "Kullanıcı Cihazı", "userType": "Kullanıcı Tipi", "role": "Rol", "roleType": "Rol Tipi", "passAgain": "Şifre(Tekrar)", "generatePass": "<PERSON><PERSON><PERSON>", "myProfile": "Profilim", "changePassword": "<PERSON><PERSON><PERSON>", "oldPass": "Eski Şifre", "newPass": "<PERSON><PERSON>", "newPassAgain": "<PERSON><PERSON>", "contracted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortTerm": "<PERSON><PERSON><PERSON>", "fullTime": "<PERSON>", "partTime": "Yarı Zamanlı", "location": "Lokasyon", "province": "İl", "district": "İlçe", "neighborhood": "Semt", "postalCode": "Posta Kodu", "country": "<PERSON><PERSON><PERSON>", "avenueStreet": "Cadde/Sokak", "buildingDoor": "Bina/Kapı No", "addressDirections": "<PERSON><PERSON>", "regionalTime": "Bölgesel Saat", "address": "<PERSON><PERSON>", "devices": "Cihazlar", "deviceName": "<PERSON><PERSON><PERSON>", "newDevice": "<PERSON><PERSON>", "brand": "<PERSON><PERSON>", "model": "Model", "version": "Versiyon", "showDevices": "Cihazları Göster", "gsm": "GSM", "gps": "GPS", "calendar": "Takvim", "day": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "month": "Ay", "today": "<PERSON><PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON><PERSON>", "tuesday": "Salı", "wednesday": "Çarşamba", "thursday": "Perşembe", "friday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON><PERSON><PERSON>", "sunday": "Pazar", "showBusinessHours": "İş saatlerini göster", "showAllDay": "<PERSON><PERSON><PERSON> gün <PERSON>", "noEventsToday": "Bu gün i<PERSON>in et<PERSON> yok!", "outOfHours": "Mesai Dışı", "calendarColor": "<PERSON><PERSON><PERSON><PERSON>", "startTime": "Başlangıç <PERSON>", "endTime": "Bitiş Zamanı", "time": "Zaman", "date": "<PERSON><PERSON><PERSON>", "dataTime": "<PERSON><PERSON>", "lastDataTime": "<PERSON>", "timeInterval": "Zaman Aralığı", "duration": "<PERSON><PERSON><PERSON>", "beginning": "Başlangıç", "finish": "Bitiş", "lastHour": "Son {number} saat", "jobList": "İş Listesi", "newJob": "<PERSON><PERSON>", "newTask": "<PERSON><PERSON>", "task": "<PERSON><PERSON><PERSON><PERSON>", "allJobs": "<PERSON><PERSON><PERSON>", "importantJobs": "<PERSON>ne<PERSON>li <PERSON>", "completedJobs": "<PERSON><PERSON><PERSON><PERSON>", "unfinishedJobs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "reportType": "<PERSON><PERSON>", "reportOperations": "<PERSON><PERSON>", "reportsList": "<PERSON><PERSON><PERSON>", "movementReport": "Hareket Raporu", "timeReport": "<PERSON><PERSON>", "exportExcel": "Excel'e Aktar", "exportPdf": "PDF'e Aktar", "rules": "<PERSON><PERSON><PERSON>", "ruleList": "<PERSON><PERSON>", "addNewRule": "<PERSON><PERSON>", "newRule": "<PERSON><PERSON>", "speedViolation": "Aşırı Hız <PERSON>ali", "stopTimeViolation": "Anlık Durma Süresi İhlali", "movementTimeViolation": "Anlık Hareket Süresi İhlali", "dailyMaxStopTimeViolation": "Günlük Maksimum Durma Süresi İhlali", "dailyMovementTimeViolation": "Günlük Hareket Süresi İhlali", "areaEntryExitViolation": "<PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON>", "suddenAcceleration": "<PERSON><PERSON>", "suddenSlowdown": "<PERSON><PERSON>", "workingTimeDelayViolation": "Mesai Zamanı Gecikme İ<PERSON>ali", "fatigueViolation": "Yorgunluk İhlali", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pushMessage": "<PERSON><PERSON>", "totalViolation": "Toplam İhlal", "instantStatus": "Anlık Durum", "instantStop": "<PERSON><PERSON><PERSON><PERSON>", "instantAct": "Anlık\nHareket", "wholeTeam": "<PERSON><PERSON><PERSON>", "team": "Ekip", "teamGroup": "Ekip/Grup", "teamGroupShort": "Ekip/Grup", "person": "<PERSON><PERSON><PERSON>", "animation": "Animasyon", "animationColors": "<PERSON><PERSON><PERSON><PERSON>", "performance": "Performans", "dailyMovementDistanceInKm": "Günlük Hareket Mesafesi (km)", "dailyDrivingDistanceInKm": "Günlük Sürüş Mesafesi (km)", "maxSpeedInKm": "Ma<PERSON><PERSON>um Hız(km)", "numberDailyAct": "Günlük Aktivite Adedi", "numberDevicesNoData": "Veri Gelmeyen Cihaz Adedi", "distance": "Mesafe", "speed": "Hız", "kmh": "Km/s", "drivingTot": "Sürüş Top.(km)", "movingTot": "Hareketli Top.(km)", "headAddress": "Baş. Adres", "lastAddress": "<PERSON>", "dayStartAddress": "<PERSON><PERSON><PERSON>", "dailyDowntime": "Günlük Durma Süresi", "dailyWalkingTime": "Günlük Yürüyüş Süresi", "dailyWalkingDistance": "Günlük Yürüyüş Mesafesi (km)", "dailyDrivingTime": "Günlük Sürüş Süresi", "dailyDrivingDistance": "Günlük Sürüş Mesafesi (km)", "vehicleTotalDistance": "Araç <PERSON>lam Mesafe (km)", "forms": "Formlar", "settings": "<PERSON><PERSON><PERSON>", "locationClosureAuthorization": "Lokasyon Kapatma İzni", "closedPortfolio": "Kapalı Portföy", "applicationInstalled": "Uygulama <PERSON>", "inAppLocationSharing": "Uygulama içi Konum Paylaşımı", "serviceIp": "Servis <PERSON>", "servicePort": "Servis Port", "companyUpdate": "Müşteri Güncelle", "minCharacterError": "<PERSON><PERSON><PERSON> sayısı en az {charLength} olabilir", "enterValidId": "Lütfen geçerli bir müşteri numarası giriniz.", "enterValidEmail": "Lütfen geçerli bir email adresi giriniz.", "enterValidPass": "Lütfen geçerli bir şifre giriniz.", "enterValidOTP": "Lütfen geçerli bir OTP kodu giriniz.", "enterValidPostalCode": "Lütfen geçerli bir posta kodu giriniz.", "enterValidName": "Lütfen geçerli bir isim giri<PERSON>.", "enterValidPhone": "Lütfen geçerli bir telefon numarası giriniz.", "enterValidWebAddress": "Lütfen geçerli bir web adresi giriniz.", "enterValidIpAddress": "Lütfen geçerli bir server IP giriniz.", "enterValidServerPort": "Lütfen geçerli bir web server port giriniz.", "enterValidLatLng": "Lütfen geçerli bir LatLng formatı giriniz.", "enterValidBuildingDoor": "Lütfen geçerli bir Bina/Kapı No giriniz.", "otp": "OTP", "signingIn": "<PERSON><PERSON><PERSON>lıyo<PERSON>", "forgetPass": "<PERSON><PERSON><PERSON>", "actType": "Aktivite Türü", "eventType": "Etkinlik Türü", "contentType": "İçerik Tipi", "conclusion": "<PERSON><PERSON><PERSON>", "content": "İçerik", "contact": "Kontak", "reason": "Sebep", "searchNameTask": "<PERSON><PERSON> (İsim veya Görev)", "jan": "Ocak", "feb": "Ş<PERSON><PERSON>", "march": "Mart", "april": "<PERSON><PERSON>", "may": "<PERSON><PERSON><PERSON>", "june": "Haziran", "july": "Temmuz", "aug": "<PERSON><PERSON><PERSON><PERSON>", "sep": "<PERSON><PERSON><PERSON><PERSON>", "oct": "<PERSON><PERSON>", "nov": "Kasım", "dec": "Aralık", "permits": "<PERSON><PERSON><PERSON>", "costs": "Ma<PERSON>ra<PERSON><PERSON>", "enter": "<PERSON><PERSON><PERSON>", "exit": "Çıkış", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectDevice": "C<PERSON>az <PERSON>", "critical": "<PERSON><PERSON><PERSON>", "worker": "Çalışan", "expenseType": "<PERSON><PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "expenseDate": "<PERSON><PERSON><PERSON><PERSON>", "creationDate": "Oluşturma Tarihi", "approved": "Onaylandı", "approve": "<PERSON><PERSON><PERSON>", "rejected": "Reddedildi", "reject": "<PERSON><PERSON>", "createCalendar": "Takvim <PERSON>", "deactivateActivate": "Deaktif/Aktif", "pleaseSelectAtLeastOneDevice": "Lütfen en az bir cihaz seçin", "deviceCount": "{count} cihaz", "personalInformation": "<PERSON><PERSON><PERSON><PERSON>", "workInformation": "Çalışma Bilgileri", "newProfilePhoto": "Yeni Profil Fotoğrafı", "firstName": "Ad<PERSON>", "lastName": "Soyadı", "pleaseEnterRequiredFields": "Lütfen zorunlu alanları doldurun", "recordSavedSuccessfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> tamamlandı", "workInformationTitle": "Çalışma Bilgileri", "workDuration": "Çalışma Süresi", "openAddress": "Açık Adres", "educationInformation": "Eğitim Bilgileri", "emergencyContactName": "Acil Durum Adı-Soyadı", "emergencyContactPhone": "Acil Durum Telefonu", "relationshipDegree": "Yakınlık <PERSON>", "educationStatus": "Durum", "documentTitle": "Başlık", "educationRecords": "Eğitim Bilgileri", "addLeaveRecordTitle": "İzin Kaydı ekle", "leaveStartDate": "İzin Başlangıç", "leaveEndDate": "İzin Bitiş", "leaveType": "İzin Türü", "documentRecordAdd": "Döküman Kaydı <PERSON>", "documentType": "Belge Türü", "missingDocument": "Eksik Belge", "viewDocument": "Belgeyi Görü<PERSON>üle", "updateDocument": "Belgeyi <PERSON>", "uploadDocument": "<PERSON><PERSON>", "addFile": "<PERSON><PERSON><PERSON>", "uploadingFiles": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "youCanOnlyUploadUpTo": "En fazla {maxFileCount} <PERSON><PERSON> y<PERSON>z", "confirmAction": "Emin misin?", "companyLocationVisit": "Firma Konumunda Ziyaret", "phoneNumberPlaceholder": "555 555 55 55", "fillAllFields": "<PERSON><PERSON>m alanları doldurunuz.", "groupRecordAdd": "Grup Kaydı ekle", "groupName": "Grup Adı", "teamSelect": "Takım <PERSON>", "loadingData": "Loading data...", "mondayShort": "PT", "tuesdayShort": "SA", "wednesdayShort": "ÇA", "thursdayShort": "PE", "fridayShort": "CU", "saturdayShort": "CT", "sundayShort": "PA", "smartLocationTechnologies": "Akıllı Konum Teknolojileri A.Ş. © 2025", "allTeam": "<PERSON><PERSON><PERSON>", "expandAll": "Tümünü genişlet", "collapseAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchMembers": "Search members...", "unknownUser": "Unknown User", "noPhone": "No Phone", "groupA": "Group A", "groupB": "Group B", "groupC": "Group C", "groupD": "Group D", "mobileUserCreatedSuccessfully": "Mobile user created successfully", "mobileUserUpdatedSuccessfully": "Mobile user updated successfully", "mobileUserCreationFailed": "Mobile user creation failed", "mobileUserUpdateFailed": "Mobil kullanıcı güncellenemedi", "create": "Oluştur", "equal": "Eşit", "notEqual": "<PERSON><PERSON><PERSON>", "startsWith": "<PERSON><PERSON>", "contains": "İçeriyor", "doesNotContain": "İçermiyor", "endsWith": "<PERSON>le biter", "null": "<PERSON><PERSON>", "notNull": "<PERSON><PERSON>", "empty": "Boş", "notEmpty": "<PERSON><PERSON> değil", "hasValue": "<PERSON><PERSON><PERSON> içeriyor", "doesNotHaveValue": "<PERSON><PERSON><PERSON> içermiyor", "and": "Ve", "or": "<PERSON><PERSON><PERSON>", "pleaseEnterAtLeastOneQuestion": "Lütfen en az bir soru ekleyiniz.", "yourAnswersHaveBeenSavedSuccessfully": "Cevaplarınız başarıyla kaydedildi.\nTeşekkürler!", "thisFormIsNotPublic": "Bu form herkese açık değildir.", "filesAreBeingUploadedPleaseWait": "Dosyalar yüklenirken lütfen bekleyiniz...", "thisFieldIsRequired": "<PERSON><PERSON> <PERSON><PERSON>", "youCanOnlyUploadUpToFiles": "En fazla {maxFileCount} <PERSON><PERSON> y<PERSON>z", "selectDate": "<PERSON><PERSON><PERSON>", "selectTime": "Saat seçiniz", "typeYourAnswerHere": "Cevabınızı buraya yazın", "invalidNumberFormat": "Geçersiz sayı formatı", "chooseOne": "<PERSON><PERSON><PERSON>", "addOption": "Seçenek ekle", "selectFieldType": "<PERSON> tü<PERSON><PERSON> se<PERSON>", "optionNumber": "Seçenek {number}", "assignedUsers": "<PERSON><PERSON><PERSON>: ", "indicatesRequiredQuestion": "* Zorunlu soruyu belirtir", "back": "<PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "addQuestion": "<PERSON><PERSON>", "noQuestionsYet": "<PERSON>n<PERSON>z soru yok", "teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>", "vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom", "customerA": "A Müşterisi", "customerB": "B Müşterisi", "customerC": "C Müşterisi", "mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi", "personalDocumentsTab": "Özlük Evrakları", "assignedAssetsTitle": "Üzerine Zimmetli Mallar", "addAssetTitle": "Zimmetli Mal <PERSON>", "assignmentAndSalaryInfoTitle": "Görevlendirme ve Maaş Bilgileri", "addAssignmentSalaryRecordTitle": "Görevlendirme ve Maaş Kaydı Ekle", "leaveUsageTitle": "<PERSON><PERSON>", "usedAnnualLeaveTitle": "Kullanılan Yıllık İzin", "remainingAnnualLeaveRightTitle": "Kalan Yıllık İzin Hakkı", "profileTitle": "Profil", "genderTitle": "Cinsiyeti", "nationalityTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maritalStatusTitle": "<PERSON><PERSON><PERSON>", "contactInformationTitle": "İletişim Bilgileri", "educationInformationTitle": "Eğitim Bilgileri", "addEducationRecordTitle": "Eğitim Kaydı Ekle", "workEmailAddressTitle": "İş E-posta Adresi", "personalEmailAddressTitle": "Kişisel E-posta Adresi", "workPhoneTitle": "İş Telefonu", "personalPhoneTitle": "Kişisel Telefonu", "emergencyContactNameTitle": "Acil Durum Adı-Soyadı", "emergencyContactPhoneTitle": "Acil Durum Telefonu", "relationshipDegreeTitle": "Yakınlık <PERSON>", "educationStatusTitle": "Durum", "educationInstitutionTitle": "<PERSON>ğ<PERSON><PERSON>", "departmentTitle": "Bölümü", "graduationDateTitle": "Mezuniyet Tarihi", "pleaseEnterValidNumber": "Lütfen geçerli bir sayı giriniz.", "pleaseEnterValidBrand": "Lütfen geçerli bir marka giriniz.", "pleaseEnterValidModel": "Lütfen geçerli bir model giriniz.", "superAdmin": "<PERSON><PERSON><PERSON>", "moderator": "Moderat<PERSON><PERSON>", "systemConstants": "Sistem <PERSON>i", "apiUrl": "Api Url", "headerCode": "Header Code", "apiKey": "Api Key", "sender": "<PERSON><PERSON><PERSON><PERSON>", "newDeviceMessage": "<PERSON><PERSON> c<PERSON>az mesaj", "mailSMTP": "Mail SMTP", "mailPort": "Mail Port", "sslActive": "SSL Aktif", "firmware": "Firmware", "currentStatus": "<PERSON><PERSON><PERSON><PERSON>", "lastDataDateAndTime": "<PERSON> <PERSON> ve <PERSON>", "odometer": "Odometre", "simCard": "<PERSON><PERSON>", "connectedCustomer": "Bağlı Müşteri", "company": {"companyOperations": "Firma <PERSON>i", "companyList": "<PERSON><PERSON>", "companyPanel": "<PERSON>rma <PERSON>i", "companies": "Firmalar", "companyUpdateSuccessful": "<PERSON>rma ba<PERSON><PERSON><PERSON><PERSON>", "companyCreateSuccessful": "<PERSON>rma başarıyla oluşturuldu", "companyCouldNotBeUpdated": "<PERSON><PERSON>", "companyCouldNotBeCreated": "Firma oluşturulamadı", "companyInfoNotFound": "Firma bilgisi bulunamadı", "pleaseSelectCompany": "Lütfen önce bir firma seçiniz."}, "personnel": {"personnel": "<PERSON><PERSON>", "personnels": "Personeller", "personalManagement": "<PERSON><PERSON>", "personalDetail": "Personel Detayı", "addNewPersonnel": "<PERSON><PERSON>", "addNewPersonnelPlus": "<PERSON><PERSON> +", "editPersonnel": "<PERSON><PERSON>", "searchPersonnel": "<PERSON><PERSON>", "personnelName": "<PERSON>el Adı", "noPersonnelFound": "<PERSON>el bulunamadı", "personalInfo": "<PERSON><PERSON><PERSON><PERSON>", "workInfo": "İş Bilgileri", "contactInfo": "İletişim Bilgileri", "workDetails": "İş Detayları", "educationInfo": "Eğitim Bilgileri", "documents": "<PERSON><PERSON><PERSON>", "personalDocuments": "<PERSON><PERSON><PERSON><PERSON>", "workType": "İş Türü", "workStartDate": "İşe Başlama Tarihi", "jobStartDate": "G<PERSON><PERSON><PERSON>", "assignmentDate": "<PERSON><PERSON>", "department": "<PERSON><PERSON><PERSON>", "position": "Pozisyon", "grossSalary": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Uyruk", "turkish": "Türk", "gender": "Cinsiyet", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın", "birthDate": "<PERSON><PERSON><PERSON>", "maritalStatus": "<PERSON><PERSON><PERSON>", "married": "<PERSON><PERSON><PERSON>", "single": "<PERSON><PERSON>", "numberOfChildren": "Çocuk Sayısı", "identityNumber": "<PERSON><PERSON>", "personalPhone": "Kişisel Telefon", "personalEmail": "Kişisel E-posta Adresi", "workPhone": "İş Telefonu", "workEmail": "İş E-posta Adresi", "employee": "Çalışan", "employees": "Çalışanlar", "selectedEmployees": "Seçili Çalışanlar"}, "education": {"educationRecords": "Eğitim Kayıtları", "addEducationRecord": "Eğitim Kaydı Ekle", "addEducationRecordPlus": "Eğitim Kaydı Ekle +", "educationInstitution": "<PERSON>ğ<PERSON><PERSON>", "graduationDate": "Mezuniyet Tarihi"}, "document": {"addDocumentRecord": "Belge Kaydı Ekle", "documentType": "Belge Türü", "missingDocument": "Eksik Belge", "viewDocument": "Belgeyi Görü<PERSON>üle", "updateDocument": "Belgeyi <PERSON>", "uploadDocument": "<PERSON><PERSON>", "addFile": "<PERSON><PERSON><PERSON>", "uploadingFiles": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "youCanOnlyUploadUpTo": "En fazla {maxFileCount} <PERSON><PERSON> y<PERSON>z"}, "leave": {"leaveCalendar": "<PERSON><PERSON>", "leaveTypes": "<PERSON><PERSON>", "leaveType": "İzin Türü", "leaveUsage": "<PERSON><PERSON>", "leaveStart": "İzin Başlangıcı", "leaveEnd": "İzin Bitişi", "leaveDuration": "<PERSON><PERSON>", "addLeaveRecord": "İzin Kaydı Ekle", "addLeaveRecordPlus": "İzin Kaydı Ekle +", "annualPaidLeave": "Yıllık ücretli izin", "maternityLeave": "Doğum izni", "paternityLeave": "Babalık izni", "marriageLeave": "Evlilik izni", "adoptionLeave": "<PERSON>v<PERSON> edinme i<PERSON>ni", "breastfeedingLeave": "Emzirme izni", "halfDayMaternityLeave": "<PERSON><PERSON><PERSON><PERSON> gün doğum izni", "excuseLeave": "Mazeret izni", "periodicControlLeave": "Periyodik kontrol izni", "disabledChildTreatmentLeave": "<PERSON><PERSON><PERSON> tedavi izni", "newJobSearchLeave": "Yeni iş arama izni", "remainingAnnualLeaveRight": "Kalan Yıllık İzin Hakkı", "usedAnnualLeave": "Kullanılan Yıllık İzin"}, "assignment": {"assignmentAndSalaryInfo": "Atama ve Maaş Bilgileri", "addAssignmentSalaryRecord": "Atama ve Maaş Bilgileri Kaydı Ekle", "addAssignmentSalaryRecordPlus": "Atama ve Maaş Bilgileri Kaydı Ekle +"}, "asset": {"assignedAssets": "Atanan Varlıklar", "addAsset": "Varlık Ekle", "addAssetRecord": "Varlık Kaydı Ekle", "assetType": "Varlık Türü", "returnDate": "<PERSON><PERSON>"}}