{"appTitle": "Smart Team Web", "start": "Get Started", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>", "registerAgreement": "I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.", "name": "Name", "surname": "Surname", "description": "Description", "active": "Active", "inactive": "Inactive", "search": "Search", "add": "Add", "edit": "Edit", "delete": "Delete", "action": "Action", "documentLabel": "Document", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "select": "Select...", "all": "All", "none": "None", "online": "Online", "offline": "Offline", "admin": "Admin", "user": "User", "userOperations": "User Operations", "userList": "User List", "addUser": "Add User", "type": "Type", "title": "Title", "group": "Group", "status": "Status", "open": "Open", "list": "List", "addNew": "Add New", "pick": "Select", "selectColor": "Select Color", "fieldRequired": "Field Required", "noDataFound": "No data found", "previous": "Previous", "next": "Next", "page": "Page", "record": "record", "waiting": "Waiting", "pending": "Pending", "accepted": "Accepted", "declined": "Declined", "done": "Done", "inProgress": "In Progress", "waitingForInfo": "Waiting for <PERSON>fo", "cancelled": "Cancelled", "overdue": "Overdue", "passive": "Passive", "potential": "Potential", "answers": "Answers", "priorityLabel": "Priority", "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "taskType": {"visit": "Visit", "collection": "Collection", "service": "Service", "onlineMeeting": "Online Meeting", "phoneCall": "Phone Call"}, "device": {"brands": {"teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>"}, "models": {"modelA": "Model A", "modelB": "Model B", "modelC": "Model C"}, "simOperators": {"vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom"}, "addDevice": "Add <PERSON>", "deviceList": "Device List", "activationDate": "Activation Date"}, "vehicle": {"addVehicle": "Add Vehicle", "vehicleList": "Vehicle List", "vehicleBrand": "Vehicle Brand", "vehicleModel": "Vehicle Model", "vehicleType": "Vehicle Type", "vehicleModels": "Vehicle Models", "vehicleModelOperations": "Vehicle Model Operations", "co2EmissionValue": "CO2 Emission Value", "plate": "Plate", "brands": {"mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi"}}, "customer": {"customers": "Customers", "addCustomer": "Add Customer", "customerA": "Customer A", "customerB": "Customer B", "customerC": "Customer C", "phoneNumber": "Phone Number", "address": "Address", "openAddress": "Open Address", "findMyLocation": "Find My Location", "fillAllFields": "Please fill all fields"}, "groups": {"groupA": "Group A", "groupB": "Group B", "groupC": "Group C", "groupD": "Group D"}, "dashboard": "Dashboard", "dashboardDetails": {"todoList": "Todo List", "instantMovements": "Instant Movements", "instantStoppage": "Instant Stoppage"}, "form": {"addForm": "Add Form", "openForm": "Open Form", "formName": "Form Name", "assignForm": "Assign Form", "goToForm": "Go to Form", "openFormDescription": "Open form, visible to everyone and cannot be assigned to individuals", "deleteFormConfirm": "Are you sure you want to delete the form titled '{title}'?", "requiredQuestion": "* Indicates a required question", "addOption": "Add option", "createForm": "Create Form", "formDeletedSuccessfully": "Form deleted successfully", "formCouldNotBeDeleted": "Form could not be deleted", "updateForm": "Update Form"}, "area": {"areaEntry": "Area Entry", "areaWaiting": "Area Waiting", "areaExit": "Area Exit", "editArea": "Edit Area", "areaName": "Area Name", "areaType": "Area Type"}, "emergency": {"emergencyContactName": "Emergency Contact Name", "emergencyPhone": "Emergency Phone", "emergencyContact": "Emergency Contact"}, "validation": {"linkCouldNotOpen": "Link could not be opened", "phoneNumberExample": "+90 555 555 55 55", "phoneNumberShort": "555 555 55 55", "pleaseSelectValid": "Please select a valid option", "thisFieldRequired": "This field is required", "invalidNumberFormat": "Invalid number format", "pleaseEnterValidNumber": "Please enter a valid number", "pleaseEnterValidBrand": "Please enter a valid brand", "pleaseEnterValidModel": "Please enter a valid model", "pleaseFillRequiredFields": "Please fill required fields", "pleaseSelectRole": "Please select a role", "pleaseSelectAtLeastOneDevice": "Please select at least one device", "pleaseSelectPersonnel": "Please select a personnel", "pleaseAddAtLeastOneQuestion": "Please add at least one question", "fillAllFields": "Please fill all fields", "passwordsDoNotMatch": "Passwords do not match", "endDateCannotBeBeforeStartDate": "End date cannot be before start date", "deviceIdAreaCantBeEmpty": "Device ID area cannot be empty", "imeiCantBeEmpty": "IMEI cannot be empty", "odometerCantBeEmpty": "Odometer cannot be empty", "simCardNoCantBeEmpty": "SIM Card No cannot be empty", "startTimeCannotBeAfterEndTime": "Start time cannot be after end time"}, "copyright": "Akıllı Konum Teknolojileri Inc. © 2025", "common": {"update": "Update", "create": "Create", "reset": "Reset", "clear": "Clear", "clearAll": "Clear All", "selectAll": "Select All", "expandAll": "Expand All", "collapseAll": "Collapse All", "viewAll": "View All", "filter": "Filter", "clearAllFilters": "Clear All Filters", "copy": "Copy", "preview": "Preview", "detail": "Detail", "details": "Details", "back": "Back", "forward": "Forward", "inspect": "Inspect", "operation": "Operation", "operations": "Operations", "result": "Result", "noResultFound": "No result found", "loadingData": "Loading data...", "pleaseWait": "Please wait...", "tryAgain": "Try again", "areYouSure": "Are you sure?", "deleteConfirmation": "Delete Confirmation", "updateSuccessful": "Update successful", "createSuccessful": "Create successful", "deleteSuccessful": "Delete successful", "operationFailed": "Operation failed", "loginSuccessful": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "send": "Send", "mailSent": "Mail sent", "mailCouldNotBeSent": "Mail could not be sent", "linkExpired": "<PERSON> expired. Please send mail again.", "passwordUpdated": "Password updated. You can login with your new password.", "passwordCouldNotBeUpdated": "Password could not be updated", "updatePassword": "Update Password", "enterYourEmailToResetPassword": "Enter your email to reset password:", "sendMail": "Send Mail", "mail": "Mail", "findMyLocation": "Find My Location", "selectOption": "Select...", "expenseType": "Expense Type", "expenseDate": "Expense Date", "imei": "IMEI", "kilometer": "Kilometer", "simOperator": "SIM Operator", "simCardNumber": "SIM Card Number", "deviceId": "Device ID", "vehicleBrand": "Vehicle Brand", "vehicleModel": "Vehicle Model", "serverKey": "Server Key", "approvalDate": "Approval Date", "permitType": "Permit Type", "permitCalendar": "Permit Calendar", "permitDuration": "Permit Duration", "permitStart": "Permit Start", "permitEnd": "Permit End", "viewDocument": "View Document", "addNewJob": "Add New Job", "formSubmitter": "Form Submitter", "submissionDate": "Submission Date", "gpsDevices": "GPS Devices", "gpsDeviceOperations": "GPS Device Operations", "gpsDeviceList": "GPS Device List", "addGpsDevice": "Add GPS Device", "editGpsDevice": "Edit GPS Device", "createGpsDeviceSuccessful": "GPS Device created successfully", "updateGpsDeviceSuccessful": "GPS Device updated successfully", "createGpsDeviceFailed": "GPS Device could not be created", "updateGpsDeviceFailed": "GPS Device could not be updated", "noResultsFound": "No results found", "searchPerson": "Search person...", "assignedUsers": "Assigned users: ", "required": "Required", "selectFieldType": "Select field type", "question": "Question", "addOption": "Add option", "addQuestion": "Add Question", "all": "All", "dailyMeetings": "Daily Meetings", "instantMovements": "Instant Movements", "instantStop": "Instant Stop", "mapScreen": "Map Screen", "editJob": "Edit Job", "todoList": "Todo List", "pleaseSelectAtLeastOneDevice": "Please select at least one device", "deactivateActivate": "Deactivate/Activate", "smartLocationTechnologies": "Smart Location Technologies Inc. © 2025", "startDate": "Start Date", "endDate": "End Date", "creationDate": "Creation Date", "selectUsers": "Select Users", "open": "Open", "responsesTo": "Responses"}, "companyType": {"commercial": "Commercial", "individual": "Individual"}, "company": {"companyOperations": "Company Operations", "companyList": "Company List", "companyPanel": "Company Panel", "companies": "Companies", "companyUpdateSuccessful": "Company updated successfully", "companyCreateSuccessful": "Company created successfully", "companyCouldNotBeUpdated": "Company could not be updated", "companyCouldNotBeCreated": "Company could not be created", "companyInfoNotFound": "Company information not found", "pleaseSelectCompany": "Please select a company first"}, "addNewCustomer": "Add New Customer", "customerId": "Customer Id", "customerName": "Customer Name", "companyName": "Company Name", "contactName": "Contact Name", "authorizedName": "Authorized Name", "authorizedEmail": "Authorized Email", "contactGSM": "Contact GSM", "authorizedGSM": "Authorized GSM", "contactEmail": "Contact Email", "taxOffice": "Tax Office", "taxNumber": "Tax Number", "customerPhone": "Customer Phone", "webAddress": "Web Address", "userName": "User Name", "password": "Password", "nameSurname": "Name Surname", "email": "Email", "phone": "Phone", "newUser": "New User", "users": "Users", "userDevice": "User Device", "userType": "User Type", "role": "Role", "roleType": "Role Type", "passAgain": "Password(Again)", "generatePass": "Generate Password", "myProfile": "My Profile", "changePassword": "Change Password", "oldPass": "Old Password", "newPass": "New Password", "newPassAgain": "New Password Again", "contracted": "Contracted", "shortTerm": "Short Term", "fullTime": "Full Time", "partTime": "Part Time", "location": "Location", "province": "Province", "district": "District", "neighborhood": "Neighborhood", "postalCode": "Postal Code", "country": "Country", "avenueStreet": "Avenue/Street", "buildingDoor": "Building/Door No", "addressDirections": "Address Directions", "regionalTime": "Regional Time", "address": "Address", "devices": "Devices", "deviceName": "Device Name", "newDevice": "New Device", "brand": "Brand", "model": "Model", "version": "Version", "showDevices": "Show Devices", "gsm": "GSM", "gps": "GPS", "calendar": "Calendar", "day": "Day", "week": "Week", "month": "Month", "today": "Today", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "showBusinessHours": "Show business hours", "showAllDay": "Show all day", "noEventsToday": "There are no events for this day!", "outOfHours": "Out of Hours", "calendarColor": "Calendar Color", "startTime": "Start Time", "endTime": "End Time", "time": "Time", "date": "Date", "dataTime": "Data Time", "lastDataTime": "Last Data Time", "timeInterval": "Time Interval", "duration": "Duration", "beginning": "Beginning", "finish": "End", "lastHour": "Last {number} hour", "jobList": "Job List", "newJob": "New Job", "newTask": "New Task", "task": "Task", "allJobs": "All Jobs", "importantJobs": "Important Jobs", "completedJobs": "Completed Jobs", "unfinishedJobs": "Unfinished Business", "reports": "Reports", "reportType": "Report Type", "reportOperations": "Report Operations", "reportsList": "Reports List", "movementReport": "Movement Report", "timeReport": "Time Report", "exportExcel": "Export to Excel", "exportPdf": "Export to PDF", "rules": "Rules", "ruleList": "Rule List", "addNewRule": "Add New Rule", "newRule": "New Rule", "speedViolation": "Excessive Speed Violation", "stopTimeViolation": "Instant Stop Time Violation", "movementTimeViolation": "Instantaneous Movement Time Violation", "dailyMaxStopTimeViolation": "Daily Maximum Stopping Time Violation", "dailyMovementTimeViolation": "Daily Movement Time Violation", "areaEntryExitViolation": "Area Entry/Exit Violation", "suddenAcceleration": "Sudden Acceleration", "suddenSlowdown": "Sudden Slowdown", "workingTimeDelayViolation": "Working Time Delay Violation", "fatigueViolation": "Fatigue Violation", "notifications": "Notifications", "pushMessage": "Push Message", "totalViolation": "Total Violation", "instantStatus": "Instant Status", "instantStop": "Instant\nStop", "instantAct": "Instant\nAction", "wholeTeam": "Whole Team", "team": "Team", "teamGroup": "Team/Group", "teamGroupShort": "Team/Group", "person": "Person", "animation": "Animation", "animationColors": "Animation Colors", "performance": "Performance", "dailyMovementDistanceInKm": "Daily Movement Distance (km)", "dailyDrivingDistanceInKm": "Daily Driving Distance (km)", "maxSpeedInKm": "Maximum Speed(km)", "numberDailyAct": "Number of Daily Activities", "numberDevicesNoData": "Number of Devices with No Data", "distance": "Distance", "speed": "Speed", "kmh": "Km/h", "drivingTot": "Driving Tot.(km)", "movingTot": "Moving Tot.(km)", "headAddress": "Head Address", "lastAddress": "Last Address", "dayStartAddress": "Day Start Address", "dailyDowntime": "Daily Downtime", "dailyWalkingTime": "Daily Walking Time", "dailyWalkingDistance": "Daily Walking Distance (km)", "dailyDrivingTime": "Daily Driving Time", "dailyDrivingDistance": "Daily Driving Distance (km)", "vehicleTotalDistance": "Vehicle Total Distance (km)", "forms": "Forms", "settings": "Settings", "locationClosureAuthorization": "Location Closure Authorization", "closedPortfolio": "Closed Portfolio", "applicationInstalled": "Application Installed", "inAppLocationSharing": "In-app Location Sharing", "serviceIp": "Service IP", "servicePort": "Service Port", "companyUpdate": "Company Update", "minCharacterError": "The number of characters can be at least {charLength}", "enterValidId": "Please enter a valid customer number.", "enterValidEmail": "Please enter a valid email address.", "enterValidPass": "Please enter a valid password.", "enterValidOTP": "Please enter a valid OTP code.", "enterValidPostalCode": "Please enter a valid postal code.", "enterValidName": "Please enter a valid name.", "enterValidPhone": "Please enter a valid phone number.", "enterValidWebAddress": "Please enter a valid web address.", "enterValidIpAddress": "Please enter a valid server IP.", "enterValidServerPort": "Please enter a valid server port.", "enterValidLatLng": "Please enter a valid lat. long. format.", "enterValidBuildingDoor": "Please enter a valid building door no.", "otp": "OTP", "signingIn": "Signing In", "forgetPass": "Forget Password", "actType": "Activity Type", "eventType": "Event Type", "contentType": "Content Type", "conclusion": "Conclusion", "content": "Content", "contact": "Contact", "reason": "Reason", "searchNameTask": "Search (Name or Task)", "jan": "January", "feb": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "aug": "August", "sep": "September", "oct": "October", "nov": "November", "dec": "December", "permits": "Permits", "costs": "Costs", "enter": "Enter", "exit": "Exit", "customers": "Customers", "selectDevice": "Select Device", "critical": "Critical", "worker": "Worker", "expenseType": "Expense Type", "amount": "Amount", "expenseDate": "Expense Date", "creationDate": "Creation Date", "approved": "Approved", "approve": "Approve", "rejected": "Rejected", "reject": "Reject", "createCalendar": "Create Calendar", "deactivateActivate": "Deactivate/Activate", "pleaseSelectAtLeastOneDevice": "Please select at least one device", "deviceCount": "{count} devices", "personalInformation": "Personal Information", "workInformation": "Work Information", "newProfilePhoto": "New Profile Photo", "firstName": "First Name", "lastName": "Last Name", "pleaseEnterRequiredFields": "Please enter required fields", "recordSavedSuccessfully": "Record saved successfully", "workInformationTitle": "Work Information", "workDuration": "Work Duration", "openAddress": "Open Address", "educationInformation": "Education Information", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "relationshipDegree": "Relationship Degree", "educationStatus": "Education Status", "documentTitle": "Document Title", "educationRecords": "Education Records", "addLeaveRecordTitle": "Add Leave Record", "leaveStartDate": "Leave Start Date", "leaveEndDate": "Leave End Date", "leaveType": "Leave Type", "documentRecordAdd": "Add Document Record", "documentType": "Document Type", "missingDocument": "Missing Document", "viewDocument": "View Document", "updateDocument": "Update Document", "uploadDocument": "Upload Document", "addFile": "Add File", "uploadingFiles": "Uploading files, please wait...", "youCanOnlyUploadUpTo": "You can only upload up to {maxFileCount} files", "confirmAction": "Are you sure?", "companyLocationVisit": "Company Location Visit", "phoneNumberPlaceholder": "555 555 55 55", "fillAllFields": "Please fill all fields", "groupRecordAdd": "Add Group Record", "groupName": "Group Name", "teamSelect": "Select Team", "loadingData": "Loading data...", "mondayShort": "MO", "tuesdayShort": "TU", "wednesdayShort": "WE", "thursdayShort": "TH", "fridayShort": "FR", "saturdayShort": "SA", "sundayShort": "SU", "smartLocationTechnologies": "Smart Location Technologies Inc. © 2025", "allTeam": "All Team", "expandAll": "Expand All", "collapseAll": "Collapse All", "driver": "Driver", "searchMembers": "Search members...", "unknownUser": "Unknown User", "noPhone": "No Phone", "groupA": "Group A", "groupB": "Group B", "groupC": "Group C", "groupD": "Group D", "mobileUserCreatedSuccessfully": "Mobile user created successfully", "mobileUserUpdatedSuccessfully": "Mobile user updated successfully", "mobileUserCreationFailed": "Mobile user creation failed", "mobileUserUpdateFailed": "Mobile user update failed", "create": "Create", "equal": "Equal", "notEqual": "Not Equal", "startsWith": "Starts With", "contains": "Contains", "doesNotContain": "Does Not Contain", "endsWith": "Ends With", "null": "<PERSON><PERSON>", "notNull": "Not Null", "empty": "Empty", "notEmpty": "Not Empty", "hasValue": "Has Value", "doesNotHaveValue": "Does Not Have Value", "and": "And", "or": "Or", "pleaseEnterAtLeastOneQuestion": "Please enter at least one question.", "yourAnswersHaveBeenSavedSuccessfully": "Your answers have been saved successfully.\nThank you!", "thisFormIsNotPublic": "This form is not public.", "filesAreBeingUploadedPleaseWait": "Files are being uploaded, please wait...", "thisFieldIsRequired": "This field is required", "youCanOnlyUploadUpToFiles": "You can only upload up to {maxFileCount} files", "selectDate": "Select Date", "selectTime": "Select Time", "typeYourAnswerHere": "Type your answer here", "invalidNumberFormat": "Invalid number format", "chooseOne": "Choose one", "addOption": "Add option", "selectFieldType": "Select field type", "optionNumber": "Option {number}", "assignedUsers": "Assigned users: ", "indicatesRequiredQuestion": "* Indicates a required question", "back": "Back", "preview": "Preview", "send": "Send", "addQuestion": "Add Question", "noQuestionsYet": "No questions yet", "teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>", "vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom", "customerA": "Customer A", "customerB": "Customer B", "customerC": "Customer C", "mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi", "personalDocumentsTab": "Personal Documents", "assignedAssetsTitle": "Assigned Assets", "addAssetTitle": "Add <PERSON>set", "assignmentAndSalaryInfoTitle": "Assignment and Salary Information", "addAssignmentSalaryRecordTitle": "Add Assignment and Salary Record", "leaveUsageTitle": "Leave Usage", "usedAnnualLeaveTitle": "Used Annual Leave", "remainingAnnualLeaveRightTitle": "Remaining Annual Leave Right", "profileTitle": "Profile", "genderTitle": "Gender", "nationalityTitle": "Nationality", "maritalStatusTitle": "Marital Status", "contactInformationTitle": "Contact Information", "educationInformationTitle": "Education Information", "addEducationRecordTitle": "Add Education Record", "workEmailAddressTitle": "Work Email Address", "personalEmailAddressTitle": "Personal Email Address", "workPhoneTitle": "Work Phone", "personalPhoneTitle": "Personal Phone", "emergencyContactNameTitle": "Emergency Contact Name", "emergencyContactPhoneTitle": "Emergency Contact Phone", "relationshipDegreeTitle": "Relationship Degree", "educationStatusTitle": "Education Status", "educationInstitutionTitle": "Education Institution", "departmentTitle": "Department", "graduationDateTitle": "Graduation Date", "pleaseEnterValidNumber": "Please enter a valid number.", "pleaseEnterValidBrand": "Please enter a valid brand.", "pleaseEnterValidModel": "Please enter a valid model.", "superAdmin": "Super Admin", "moderator": "Moderator", "systemConstants": "System Constants", "apiUrl": "Api Url", "headerCode": "Header Code", "apiKey": "Api Key", "sender": "Sender", "newDeviceMessage": "New Device Message", "mailSMTP": "Mail SMTP", "mailPort": "Mail Port", "sslActive": "SSL Active", "firmware": "Firmware", "currentStatus": "Current Status", "lastDataDateAndTime": "Last Data Date and Time", "odometer": "Odometer", "simCard": "SIM Card", "connectedCustomer": "Connected Customer", "personnel": {"personnel": "Personnel", "personnels": "Personnel List", "personalManagement": "Personal Management", "personalDetail": "Personal Detail", "addNewPersonnel": "Add New Personnel", "addNewPersonnelPlus": "Add New Personnel +", "editPersonnel": "Edit Personnel", "searchPersonnel": "Search Personnel", "personnelName": "Personnel Name", "noPersonnelFound": "No personnel found", "personalInfo": "Personal Information", "workInfo": "Work Information", "contactInfo": "Contact Information", "workDetails": "Work Details", "educationInfo": "Education Information", "documents": "Documents", "personalDocuments": "Personal Documents", "workType": "Work Type", "workStartDate": "Work Start Date", "jobStartDate": "Job Start Date", "assignmentDate": "Assignment Date", "department": "Department", "position": "Position", "grossSalary": "Gross Salary", "nationality": "Nationality", "turkish": "Turkish", "gender": "Gender", "male": "Male", "female": "Female", "birthDate": "Birth Date", "maritalStatus": "Marital Status", "married": "Married", "single": "Single", "numberOfChildren": "Number of Children", "identityNumber": "Identity Number", "personalPhone": "Personal Phone", "personalEmail": "Personal Email Address", "workPhone": "Work Phone", "workEmail": "Work Email Address", "employee": "Employee", "employees": "Employees", "selectedEmployees": "Selected Employees"}, "education": {"educationRecords": "Education Records", "addEducationRecord": "Add Education Record", "addEducationRecordPlus": "Add Education Record +", "educationInstitution": "Education Institution", "graduationDate": "Graduation Date"}, "document": {"addDocumentRecord": "Add Document Record", "documentType": "Document Type", "missingDocument": "Missing Document", "viewDocument": "View Document", "updateDocument": "Update Document", "uploadDocument": "Upload Document", "addFile": "Add File", "uploadingFiles": "Uploading files, please wait...", "youCanOnlyUploadUpTo": "You can only upload up to {maxFileCount} files"}, "leave": {"leaveCalendar": "Leave Calendar", "leaveTypes": "Leave Types", "leaveType": "Leave Type", "leaveUsage": "Leave Usage", "leaveStart": "Leave Start", "leaveEnd": "Leave End", "leaveDuration": "Leave Duration", "addLeaveRecord": "Add Leave Record", "addLeaveRecordPlus": "Add Leave Record +", "annualPaidLeave": "Annual paid leave", "maternityLeave": "Maternity leave", "paternityLeave": "Paternity leave", "marriageLeave": "Marriage leave", "adoptionLeave": "Adoption leave", "breastfeedingLeave": "Breastfeeding leave", "halfDayMaternityLeave": "Half day maternity leave", "excuseLeave": "Excuse leave", "periodicControlLeave": "Periodic control leave", "disabledChildTreatmentLeave": "Disabled child treatment leave", "newJobSearchLeave": "New job search leave", "remainingAnnualLeaveRight": "Remaining Annual Leave Right", "usedAnnualLeave": "Used Annual Leave"}, "assignment": {"assignmentAndSalaryInfo": "Assignment and Salary Information", "addAssignmentSalaryRecord": "Add Assignment and Salary Information Record", "addAssignmentSalaryRecordPlus": "Add Assignment and Salary Information Record +"}, "asset": {"assignedAssets": "Assigned Assets", "addAsset": "Add <PERSON>set", "addAssetRecord": "Add Asset Record", "assetType": "Asset Type", "returnDate": "Return Date"}}